# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RunEvalModelJob, type: :job do
  let!(:user) { create(:user) }
  let!(:prompt_eval) do
    create(:prompt_eval,
           user: user,
           prompt: 'You are a helpful assistant',
           params: { temperature: 0.7, max_tokens: 100 },
           request_raw: {
             system_instruction: 'You are a helpful assistant',
             stream_options: { include_usage: true },
             temperature: 1,
             messages: [
               { role: 'user', content: 'temperature: 0.7' },
               { role: 'user', content: 'max_tokens: 100' }
             ]
           })
  end
  let!(:model_bank) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:prompt_eval_result) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank,
           status: 'pending',
           result_text: '')
  end

  let(:mock_client) { instance_double(OpenAI::Client) }
  let(:mock_chat) { instance_double(Object) }

  before do
    allow(OpenAI::Client).to receive(:new).and_return(mock_client)
    allow(mock_client).to receive(:chat).and_return(mock_chat)
    allow(Rails.application.credentials).to receive(:openrouter_key).and_return('test_key')
  end

  describe '#perform' do
    context 'when successful' do
      let(:stream_chunks) do
        [
          { 'choices' => [{ 'delta' => { 'content' => 'Hello' } }] },
          { 'choices' => [{ 'delta' => { 'content' => ' world' } }] },
          { 'choices' => [{ 'delta' => { 'content' => '!' } }] }
        ]
      end

      before do
        allow(mock_client).to receive(:chat) do |parameters:|
          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end
      end

      it 'processes the prompt eval result successfully' do
        expect { RunEvalModelJob.perform_now(prompt_eval_result.id) }
          .not_to raise_error

        prompt_eval_result.reload
        expect(prompt_eval_result.status).to eq('completed')
        expect(prompt_eval_result.result_text).to eq('Hello world!')
        expect(prompt_eval_result.response_raw).to eq({
                                                        'raw' => [
                                                          { 'content' => 'Hello' },
                                                          { 'content' => ' world' },
                                                          { 'content' => '!' }
                                                        ]
                                                      })
        expect(prompt_eval_result.error_message).to be_nil
      end

      it 'updates status to running before processing' do
        allow(mock_client).to receive(:chat) do |parameters:|
          prompt_eval_result.reload
          expect(prompt_eval_result.status).to eq('running')

          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end

        RunEvalModelJob.perform_now(prompt_eval_result.id)
      end

      it 'sets the model from model_bank.code' do
        expected_parameters = nil
        allow(mock_client).to receive(:chat) do |parameters:|
          expected_parameters = parameters
          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end

        RunEvalModelJob.perform_now(prompt_eval_result.id)

        expect(expected_parameters[:model]).to eq('gpt-4o')
      end

      it 'initializes OpenAI client with correct credentials' do
        expect(OpenAI::Client).to receive(:new).with(
          access_token: 'test_key',
          uri_base: 'https://openrouter.ai/api/v1'
        )

        RunEvalModelJob.perform_now(prompt_eval_result.id)
      end

      it 'deep symbolizes the request_raw keys' do
        expected_parameters = nil
        allow(mock_client).to receive(:chat) do |parameters:|
          expected_parameters = parameters
          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end

        RunEvalModelJob.perform_now(prompt_eval_result.id)

        expect(expected_parameters[:system_instruction]).to eq('You are a helpful assistant')
        expect(expected_parameters[:stream_options]).to eq({ include_usage: true })
        expect(expected_parameters[:temperature]).to eq(1)
        expect(expected_parameters[:messages]).to be_an(Array)
      end
    end

    context 'when API call fails' do
      before do
        allow(mock_client).to receive(:chat).and_raise(StandardError.new('API rate limit exceeded'))
      end

      it 'updates status to failed and sets error message' do
        RunEvalModelJob.perform_now(prompt_eval_result.id)

        prompt_eval_result.reload
        expect(prompt_eval_result.status).to eq('failed')
        expect(prompt_eval_result.error_message).to eq('API rate limit exceeded')
        expect(prompt_eval_result.result_text).to eq('')
      end
    end

    context 'when prompt_eval_result is not found' do
      it 'raises ActiveRecord::RecordNotFound' do
        expect { RunEvalModelJob.perform_now(99_999) }
          .to raise_error(ActiveRecord::RecordNotFound)
      end
    end

    context 'when stream chunks have different structures' do
      let(:stream_chunks) do
        [
          { 'choices' => [{ 'delta' => { 'content' => 'Hello' } }] },
          { 'choices' => [{ 'delta' => { 'role' => 'assistant' } }] },
          { 'choices' => [{ 'delta' => { 'content' => ' world' } }] },
          { 'choices' => [{ 'delta' => {} }] }
        ]
      end

      before do
        allow(mock_client).to receive(:chat) do |parameters:|
          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end
      end

      it 'handles chunks with missing content gracefully' do
        RunEvalModelJob.perform_now(prompt_eval_result.id)

        prompt_eval_result.reload
        expect(prompt_eval_result.result_text).to eq('Hello world')
      end

      it 'accumulates all delta content in response_raw' do
        RunEvalModelJob.perform_now(prompt_eval_result.id)

        prompt_eval_result.reload
        expect(prompt_eval_result.response_raw['raw']).to include({ 'content' => 'Hello' })
        expect(prompt_eval_result.response_raw['raw']).to include({ 'content' => ' world' })
      end
    end

    context 'when stream chunks are empty or nil' do
      let(:stream_chunks) do
        [
          { 'choices' => [{ 'delta' => { 'content' => 'Hello' } }] },
          { 'choices' => [{ 'delta' => nil }] },
          { 'choices' => [] },
          { 'choices' => [{ 'delta' => { 'content' => ' world' } }] }
        ]
      end

      before do
        allow(mock_client).to receive(:chat) do |parameters:|
          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end
      end

      it 'handles nil and empty chunks gracefully' do
        RunEvalModelJob.perform_now(prompt_eval_result.id)

        prompt_eval_result.reload
        expect(prompt_eval_result.result_text).to eq('Hello world')
      end
    end

    context 'when model_bank has different code' do
      let!(:model_bank) do
        create(:model_bank, name: 'GPT-4o Mini', code: 'gpt-4o-mini', input_rate: 0.0003, output_rate: 0.0012)
      end
      let!(:prompt_eval_result) do
        create(:prompt_eval_result,
               prompt_eval: prompt_eval,
               model_bank: model_bank,
               status: 'pending',
               result_text: '')
      end

      let(:stream_chunks) do
        [
          { 'choices' => [{ 'delta' => { 'content' => 'Mini response' } }] }
        ]
      end

      before do
        allow(mock_client).to receive(:chat) do |parameters:|
          expect(parameters[:model]).to eq('gpt-4o-mini')
          stream_proc = parameters[:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end
      end

      it 'uses the correct model code from model_bank' do
        RunEvalModelJob.perform_now(prompt_eval_result.id)

        prompt_eval_result.reload
        expect(prompt_eval_result.result_text).to eq('Mini response')
      end
    end
  end

  describe 'queue configuration' do
    it 'uses the default queue' do
      expect(RunEvalModelJob.queue_name).to eq('default')
    end
  end
end
