# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mailer::OnboardOrganizationMailerJob, type: :job do
  let!(:organization) do
    Organization.create!(name: 'Test')
  end

  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:membership) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  it 'should send email to user' do
    expect(OnboardOrganizationMailer).to receive(:with).with(membership: membership).and_call_original
    expect_any_instance_of(OnboardOrganizationMailer).to receive(:send_email).and_call_original
    Mailer::OnboardOrganizationMailerJob.perform_now(membership)
  end
end
