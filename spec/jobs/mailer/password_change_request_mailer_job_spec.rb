# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mailer::PasswordChangeRequestMailerJob, type: :job do
  let!(:organization) { create(:organization, code: 'testorg') }
  let!(:user) { create(:user, display_name: 'Test User', email: '<EMAIL>') }

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:token_request) do
    TokenRequest.create!(
      user_id: user.id,
      email: user.email,
      request_code: SecureRandom.hex(64),
      request_status: 'requested',
      request_expiry_date: Time.current + 7.days,
      purpose: 'change_password'
    )
  end

  it 'should send email to user' do
    expect(PasswordChangeRequestMailer).to receive(:with).with(token_request: token_request).and_call_original
    expect_any_instance_of(PasswordChangeRequestMailer).to receive(:send_email).and_call_original
    Mailer::PasswordChangeRequestMailerJob.perform_now(token_request)
  end
end
