# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mailer::UserInvitationMailerJob, type: :job do
  let!(:organization) do
    Organization.create!(name: 'Test')
  end

  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:membership) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:organization_team) do
    OrganizationTeam.create!(
      name: 'Test',
      organization_id: organization.id
    )
  end

  let!(:user_invitation) do
    UserInvitation.create!(
      email: '<EMAIL>',
      invitation_status: 'invited',
      invitation_code: 'test',
      invitation_expiry_date: Time.current + 7.days,
      organization_id: organization.id,
      invited_by_membership_id: membership.id,
      role: 1,
      invited_to_organization_team_id: organization_team.id
    )
  end

  it 'should send email to user' do
    expect(UserInvitationMailer).to receive(:with).with(user_invitation: user_invitation).and_call_original
    expect_any_instance_of(UserInvitationMailer).to receive(:send_email).and_call_original
    Mailer::UserInvitationMailerJob.perform_now(user_invitation.id)
  end

  it 'should not send email to user if user invitation not found' do
    expect(UserInvitationMailer).not_to receive(:with)
    Mailer::UserInvitationMailerJob.perform_now(0)
  end
end
