# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AgentWorkflowRunJob, type: :job do
  let!(:user) { create(:user) }
  let!(:agent_workflow) { create(:agent_workflow, created_by: user) }
  let!(:agent_workflow_run) do
    create(:agent_workflow_run, agent_workflow: agent_workflow, user: user, status: 'pending')
  end
  let!(:model_bank) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }

  # Create agent nodes
  let!(:agent_node1) do
    create(:agent_workflow_node,
           agent_workflow: agent_workflow,
           workflow_type: 'agent',
           model_bank: model_bank,
           order_level: 1,
           data: {
             'system_prompt' => 'You are a helpful assistant',
             'content' => 'Tell me about AI',
             'temperature' => 0.7,
             'name' => 'Agent 1'
           })
  end

  let!(:agent_node2) do
    create(:agent_workflow_node,
           agent_workflow: agent_workflow,
           workflow_type: 'agent',
           model_bank: model_bank,
           order_level: 2,
           data: {
             'system_prompt' => 'You are a helpful assistant',
             'content' => 'Tell me about ML',
             'temperature' => 0.7,
             'name' => 'Agent 2'
           })
  end

  # Create merger node
  let!(:merger_node) do
    create(:agent_workflow_node,
           agent_workflow: agent_workflow,
           workflow_type: 'merger',
           model_bank: model_bank,
           order_level: 3,
           data: {})
  end

  # Create node runs
  let!(:agent_node_run1) do
    create(:agent_workflow_node_run,
           agent_workflow_node: agent_node1,
           agent_workflow_run: agent_workflow_run,
           user: user,
           status: 'pending',
           data: agent_node1.data)
  end

  let!(:agent_node_run2) do
    create(:agent_workflow_node_run,
           agent_workflow_node: agent_node2,
           agent_workflow_run: agent_workflow_run,
           user: user,
           status: 'pending',
           data: agent_node2.data)
  end

  let!(:merger_node_run) do
    create(:agent_workflow_node_run,
           agent_workflow_node: merger_node,
           agent_workflow_run: agent_workflow_run,
           user: user,
           status: 'pending',
           data: {})
  end

  let(:mock_client) { instance_double(OpenAI::Client) }

  before do
    allow(OpenAI::Client).to receive(:new).and_return(mock_client)
    allow(Rails.application.credentials).to receive(:openrouter_key).and_return('test_key')
  end

  describe '#perform' do
    context 'when successful' do
      let(:stream_chunks) do
        [
          { 'choices' => [{ 'delta' => { 'content' => 'Hello' } }] },
          { 'choices' => [{ 'delta' => { 'content' => ' world' } }] },
          { 'choices' => [{ 'delta' => { 'content' => '!' } }] },
          { 'usage' => { 'prompt_tokens' => 10, 'completion_tokens' => 20, 'total_tokens' => 30 } }
        ]
      end

      before do
        allow(mock_client).to receive(:chat) do |parameters|
          stream_proc = parameters[:parameters][:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end
      end

      it 'processes the workflow run successfully' do
        expect { AgentWorkflowRunJob.perform_now(agent_workflow_run.id) }
          .not_to raise_error

        agent_workflow_run.reload
        expect(agent_workflow_run.status).to eq('completed')

        agent_node_run1.reload
        expect(agent_node_run1.status).to eq('completed')
        expect(agent_node_run1.data['response']).to eq('Hello world!')

        agent_node_run2.reload
        expect(agent_node_run2.status).to eq('completed')
        expect(agent_node_run2.data['response']).to eq('Hello world!')

        merger_node_run.reload
        expect(merger_node_run.status).to eq('completed')
        expect(merger_node_run.data['content']).to include('Agent 1: Hello world!')
        expect(merger_node_run.data['content']).to include('Agent 2: Hello world!')
      end

      it 'sets credit_usage data for each node run' do
        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)

        agent_node_run1.reload
        expect(agent_node_run1.data['credit_usage']).to be_present
        expect(agent_node_run1.data['credit_usage']['input']).to eq(0.1) # 10 tokens * 0.01
        expect(agent_node_run1.data['credit_usage']['output']).to eq(0.6) # 20 tokens * 0.03

        agent_node_run2.reload
        expect(agent_node_run2.data['credit_usage']).to be_present
        expect(agent_node_run2.data['credit_usage']['input']).to eq(0.1) # 10 tokens * 0.01
        expect(agent_node_run2.data['credit_usage']['output']).to eq(0.6) # 20 tokens * 0.03

        merger_node_run.reload
        expect(merger_node_run.data['credit_usage']).to be_present
        expect(merger_node_run.data['credit_usage']['input']).to eq(0.1) # 10 tokens * 0.01
        expect(merger_node_run.data['credit_usage']['output']).to eq(0.6) # 20 tokens * 0.03
      end

      it 'updates workflow run status to running before processing' do
        allow(mock_client).to receive(:chat) do |parameters|
          agent_workflow_run.reload
          expect(agent_workflow_run.status).to eq('running')

          stream_proc = parameters[:parameters][:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
          nil
        end

        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)
      end

      it 'sets the model from model_bank.code' do
        expect(mock_client).to receive(:chat) do |parameters|
          expect(parameters[:parameters][:model]).to eq('gpt-4o')
          stream_proc = parameters[:parameters][:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end.at_least(:once)

        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)
      end

      it 'initializes OpenAI client with correct credentials' do
        expect(OpenAI::Client).to receive(:new).with(
          access_token: 'test_key',
          uri_base: 'https://openrouter.ai/api/v1'
        )

        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)
      end
    end

    context 'when agent runner fails' do
      before do
        allow(mock_client).to receive(:chat) do |_parameters|
          raise StandardError.new('API rate limit exceeded')
        end
      end

      it 'updates workflow run status to failed and sets error message' do
        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)

        agent_workflow_run.reload
        expect(agent_workflow_run.status).to eq('failed')
        expect(agent_workflow_run.error_message).to include('API rate limit exceeded')

        agent_node_run1.reload
        expect(agent_node_run1.status).to eq('failed')
        expect(agent_node_run1.error_message).to include('API rate limit exceeded')
      end

      it 'stops processing after first failure' do
        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)

        agent_node_run2.reload
        expect(agent_node_run2.status).to eq('pending')

        merger_node_run.reload
        expect(merger_node_run.status).to eq('pending')
      end
    end

    context 'when workflow run is not found' do
      it 'raises ActiveRecord::RecordNotFound' do
        expect { AgentWorkflowRunJob.perform_now(99_999) }
          .to raise_error(ActiveRecord::RecordNotFound)
      end
    end

    context 'when stream chunks have different structures' do
      let(:stream_chunks) do
        [
          { 'choices' => [{ 'delta' => { 'content' => 'Hello' } }] },
          { 'choices' => [{ 'delta' => { 'role' => 'assistant' } }] },
          { 'choices' => [{ 'delta' => { 'content' => ' world' } }] },
          { 'choices' => [{ 'delta' => {} }] }
        ]
      end

      before do
        allow(mock_client).to receive(:chat) do |parameters|
          stream_proc = parameters[:parameters][:stream]
          stream_chunks.each { |chunk| stream_proc.call(chunk, 0) }
        end
      end

      it 'handles chunks with missing content gracefully' do
        AgentWorkflowRunJob.perform_now(agent_workflow_run.id)

        agent_node_run1.reload
        expect(agent_node_run1.status).to eq('completed')
        expect(agent_node_run1.data['response']).to eq('Hello world')
      end
    end
  end

  describe 'queue configuration' do
    it 'uses the default queue' do
      expect(AgentWorkflowRunJob.queue_name).to eq('default')
    end
  end

  describe '#credit_usage' do
    let(:job) { AgentWorkflowRunJob.new }
    let(:model) { model_bank }

    context 'when usage_metadata is present' do
      let(:usage_metadata) do
        { 'prompt_tokens' => 100, 'completion_tokens' => 50, 'total_tokens' => 150 }
      end

      it 'calculates credit usage based on model rates' do
        result = job.send(:credit_usage, usage_metadata, model)

        # Input rate is 0.01, so 100 tokens * 0.01 = 1.0
        expect(result[:input]).to eq(1.0)

        # Output rate is 0.03, so 50 tokens * 0.03 = 1.5
        expect(result[:output]).to eq(1.5)
      end

      it 'rounds to 9 decimal places' do
        # Create a model with rates that will produce long decimals
        special_model = create(:model_bank, input_rate: 0.**********, output_rate: 0.**********)
        result = job.send(:credit_usage, usage_metadata, special_model)

        # 100 tokens * 0.********** = 1.********
        expect(result[:input]).to eq(1.********)

        # 50 tokens * 0.********** = 4.*********, rounded to 9 decimal places = 4.*********
        expect(result[:output]).to eq(4.*********)
      end
    end

    context 'when usage_metadata is nil' do
      it 'returns zero values' do
        result = job.send(:credit_usage, nil, model)
        expect(result[:input]).to eq(0)
        expect(result[:output]).to eq(0)
      end
    end

    context 'when usage_metadata is empty' do
      it 'returns zero values' do
        result = job.send(:credit_usage, {}, model)
        expect(result[:input]).to eq(0)
        expect(result[:output]).to eq(0)
      end
    end
  end
end
