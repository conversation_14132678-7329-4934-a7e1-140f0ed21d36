# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RefreshOrganizationTokensJob, type: :job do
  let!(:organization) do
    Organization.create!(name: 'Test')
  end

  let!(:org_plan) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      monthly_credits_refresh: 100,
      max_members: 10,
      max_workspaces: 10,
      purchased_credits: 100,
      refresh_date: 1
    )
  end

  let!(:org_plan2) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      monthly_credits_refresh: 200,
      max_members: 20,
      max_workspaces: 20,
      purchased_credits: 200,
      refresh_date: 2
    )
  end

  it 'refresh organization tokens on that day' do
    allow(Time).to receive(:current).and_return(Time.new(2024, 10, 1))
    RefreshOrganizationTokensJob.perform_now

    org_plan.reload
    org_plan2.reload

    expect(org_plan.remaining_monthly_credits).to eq(100)
    expect(org_plan2.remaining_monthly_credits).to eq(0)

    org_credit_histories = CreditHistory.where(organization_id: organization.id)
    expect(org_credit_histories.count).to eq(1)
  end

  it 'not refresh any organization tokens on end of month' do
    allow(Time).to receive(:current).and_return(Time.new(2024, 10, 31))
    RefreshOrganizationTokensJob.perform_now

    org_plan.reload
    org_plan2.reload

    expect(org_plan.remaining_monthly_credits).to eq(0)
    expect(org_plan2.remaining_monthly_credits).to eq(0)

    org_credit_histories = CreditHistory.where(organization_id: organization.id)
    expect(org_credit_histories.count).to eq(0)
  end
end
