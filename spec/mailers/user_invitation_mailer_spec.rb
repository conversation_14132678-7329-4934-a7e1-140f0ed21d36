# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UserInvitationMailer, type: :mailer do
  let!(:organization) { create(:organization, code: 'testorg') }

  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:membership) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:organization_team) do
    OrganizationTeam.create!(
      name: 'Test',
      organization_id: organization.id
    )
  end

  let!(:user_invitation) do
    UserInvitation.create!(
      email: '<EMAIL>',
      invitation_status: 'invited',
      invitation_code: 'test',
      invitation_expiry_date: Time.current + 7.days,
      organization_id: organization.id,
      invited_by_membership_id: membership.id,
      role: -1,
      invited_to_organization_team_id: organization_team.id
    )
  end

  let(:mail) do
    mail_object = nil

    allow(UserInvitationMailer).to receive(:with).and_wrap_original do |obj, *args|
      mail_object = obj.call(*args)
    end

    Mailer::UserInvitationMailerJob.new.perform(user_invitation.id)
    mail_object.send_email
  end

  context 'when invited as partner admin' do
    it 'renders email correctly' do
      user_invitation.update!(role: -1)

      expect(mail.subject).to eq("You've Been Invited to Join #{ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'}")
      expect(mail.to).to eq(['<EMAIL>'])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.body.encoded).to include('Welcome to the BrandRev.ai Platform.')
      expect(mail.body.encoded).to include('We are excited to have you on board as our strategic partner.')
      expect(mail.body.encoded).to include('You have been invited as Channel Partner Admin on')
      # acceopt link
      expect(mail.body.encoded).to include('http://admin-staging.brandrev.ai/accept-invitation?invite_code=test&code=testorg&email=<EMAIL>&name=testuser1')
    end
  end

  context 'when invited as admin platform' do
    it 'renders email correctly' do
      user_invitation.update!(role: -3)

      expect(mail.subject).to eq("You've Been Invited to Join #{ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'}")
      expect(mail.to).to eq(['<EMAIL>'])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.body.encoded).to include('You have been invited as an Admin to the BrandRev.ai <NAME_EMAIL>.')
      # acceopt link
      expect(mail.body.encoded).to include('http://admin-staging.brandrev.ai/accept-invitation?invite_code=test&code=testorg&email=<EMAIL>&name=testuser1')
    end
  end

  context 'when invited as member' do
    it 'renders email correctly' do
      user_invitation.update!(role: 3)

      expect(mail.subject).to eq("You've Been Invited to Join #{ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'}")
      expect(mail.to).to eq(['<EMAIL>'])

      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.body.encoded).to include('Welcome to the BrandRev.ai Platform.')
      expect(mail.body.encoded).to include("We are excited to have you onboard as a Member of #{organization.name} with ID #{organization.code}.")
      # acceopt link
      expect(mail.body.encoded).to include('http://app-staging.brandrev.ai/accept-invitation?invite_code=test&code=testorg&email=<EMAIL>&name=testuser1')
      # list of tasks
      expect(mail.body.encoded).to include('Reliably and easily use AI agents for specific use cases')
      expect(mail.body.encoded).to include('Create, update & modify your very own AI Agents')
      expect(mail.body.encoded).to include('Utilize and share AI agents across department and teams in your workspace')
    end
  end

  context 'when invited as owner' do
    it 'renders email correctly' do
      user_invitation.update!(role: 0)

      expect(mail.subject).to eq("You've Been Invited to Join #{ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'}")
      expect(mail.to).to eq(['<EMAIL>'])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.body.encoded).to include('Welcome to the BrandRev.ai Platform.')
      expect(mail.body.encoded).to include("We are excited to have you onboard as a Workspace Owner of #{organization.name} with ID #{organization.code}.")
      # acceopt link
      expect(mail.body.encoded).to include('http://app-staging.brandrev.ai/accept-invitation?invite_code=test&code=testorg&email=<EMAIL>&name=testuser1')
      # list of tasks
      expect(mail.body.encoded).to include('Onboard users on your workspaces')
      expect(mail.body.encoded).to include('Onboard admins to assist you with the management of your workspace')
      expect(mail.body.encoded).to include('Create and modify your very own AI agents')
    end
  end
end
