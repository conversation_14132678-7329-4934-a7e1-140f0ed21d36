# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PasswordChangeRequestMailer, type: :mailer do
  let!(:organization) { create(:organization, code: 'testorg') }

  let!(:admin) { create(:user, display_name: 'Admin', email: '<EMAIL>') }

  let!(:membership) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:organization_team) do
    OrganizationTeam.create!(
      name: 'Test',
      organization_id: organization.id
    )
  end

  let!(:token_request) do
    TokenRequest.create!(
      user_id: admin.id,
      email: admin.email,
      request_code: SecureRandom.hex(64),
      request_status: 'requested',
      request_expiry_date: Time.current + 7.days,
      purpose: 'change_password'
    )
  end

  let(:mail) do
    mail_object = nil

    allow(PasswordChangeRequestMailer).to receive(:with).and_wrap_original do |obj, *args|
      mail_object = obj.call(*args)
    end

    Mailer::PasswordChangeRequestMailerJob.new.perform(token_request)
    mail_object.send_email
  end

  context 'when user is admin platform' do
    it 'renders email correctly' do
      membership.update!(role: -4)

      expect(mail.subject).to eq("You Have Requested to Change Your #{ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'} Account's Password")
      expect(mail.to).to eq(['<EMAIL>'])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.body.encoded).to include('Hi Admin,')
      expect(mail.body.encoded).to include('We have received a request to reset your password on the Brandrev.ai Admin Platform')
      expect(mail.body.encoded).to include('http://admin-staging.brandrev.ai/reset-password?request_code=')
      expect(mail.body.encoded).to include('&code=testorg')
    end
  end

  context 'when user is workspace member' do
    it 'renders email correctly' do
      membership.update!(role: 3)

      expect(mail.subject).to eq("You Have Requested to Change Your #{ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'} Account's Password")
      expect(mail.to).to eq(['<EMAIL>'])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.body.encoded).to include('Hi Admin,')
      expect(mail.body.encoded).to include('We have received a request to reset your password on the Brandrev.ai testorg Workspace')
      expect(mail.body.encoded).to include('http://app-staging.brandrev.ai/reset-password?request_code=')
      expect(mail.body.encoded).to include('&code=testorg')
    end
  end
end
