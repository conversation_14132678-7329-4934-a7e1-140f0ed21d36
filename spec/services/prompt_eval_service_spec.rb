# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PromptEvalService, type: :service do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:model_bank_1) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:model_bank_2) do
    create(:model_bank, name: 'GPT-4o Mini', code: 'gpt-4o-mini', input_rate: 0.0003, output_rate: 0.0012)
  end
  let!(:prompt_eval) do
    create(:prompt_eval,
           user: user,
           prompt: 'You are a helpful assistant',
           params: { temperature: 0.7, max_tokens: 100 })
  end
  let!(:prompt_eval_result_1) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank_1,
           result_text: 'I am a helpful assistant',
           status: 'completed')
  end
  let!(:prompt_eval_result_2) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank_2,
           result_text: 'I am a helpful assistant',
           status: 'completed')
  end

  let(:service) { described_class.new(user) }

  describe '#index' do
    it 'returns prompt evals filtered by params' do
      result = service.index({})

      expect(result).to be_a(OpenStruct)
      expect(result.prompt_evals).to be_present
    end

    it 'uses PromptEvals repository for filtering' do
      expect_any_instance_of(PromptEvals).to receive(:filter).with({}).and_return([])

      service.index({})
    end
  end

  describe '#show' do
    context 'when all results are completed' do
      before do
        PromptEvalResult.where(prompt_eval_id: prompt_eval.id).update_all(status: 'completed')
      end

      it 'returns prompt eval with completed status' do
        result = service.show(prompt_eval.id)

        expect(result).to be_a(OpenStruct)
        expect(result.prompt_eval).to eq(prompt_eval)
        expect(result.status).to eq('completed')
      end
    end

    context 'when some results are pending' do
      before do
        prompt_eval_result_1.update!(status: 'pending')
        prompt_eval_result_2.update!(status: 'completed')
      end

      it 'returns prompt eval with pending status' do
        result = service.show(prompt_eval.id)

        expect(result.status).to eq('pending')
      end
    end

    context 'when some results are failed' do
      before do
        prompt_eval_result_1.update!(status: 'failed')
        prompt_eval_result_2.update!(status: 'completed')
      end

      it 'returns prompt eval with failed status' do
        result = service.show(prompt_eval.id)

        expect(result.status).to eq('failed')
      end
    end

    context 'when prompt eval does not exist' do
      it 'raises ActiveRecord::RecordNotFound' do
        expect { service.show(99_999) }
          .to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe '#evaluate' do
    let(:valid_params) do
      {
        model_bank_ids: [model_bank_1.id, model_bank_2.id],
        prompt: 'You are a helpful assistant that helps users with their questions',
        params: {
          temperature: 0.7,
          'Input 1' => 'Hello world',
          'Input 2' => 'Goodbye world'
        }
      }
    end

    let(:expected_user_message) do
      <<~USER_MESSAGE
        Input 1: Hello world
        Input 2: Goodbye world
      USER_MESSAGE
    end

    context 'with valid parameters' do
      it 'creates a new prompt eval' do
        expect { service.evaluate(valid_params) }
          .to change(PromptEval, :count).by(1)
      end

      it 'creates prompt eval results for each model bank' do
        prompt_eval = service.evaluate(valid_params)

        prompt_eval_results = PromptEvalResult.where(prompt_eval_id: prompt_eval.id)
        expect(prompt_eval_results.count).to eq(2)
        expect(prompt_eval_results.map(&:model_bank_id)).to match_array([model_bank_1.id, model_bank_2.id])
        expect(prompt_eval_results.map(&:status)).to all(eq('pending'))
      end

      it 'sets user_id on the prompt eval' do
        prompt_eval = service.evaluate(valid_params)

        expect(prompt_eval.user_id).to eq(user.id.to_s)
      end

      it 'enqueues RunEvalModelJob for each result' do
        expect(RunEvalModelJob).to receive(:perform_later).twice

        service.evaluate(valid_params)
      end

      it 'initializes request_raw correctly on prompt eval' do
        prompt_eval = service.evaluate(valid_params)

        expect(prompt_eval.request_raw).to include(
          'temperature' => 0.7,
          'messages' => [
            { 'role' => 'system', 'content' => 'You are a helpful assistant that helps users with their questions' },
            { 'role' => 'user', 'content' => expected_user_message }
          ]
        )
      end

      it 'sets params on prompt eval' do
        prompt_eval = service.evaluate(valid_params)

        expect(prompt_eval.params).to eq({ 'temperature' => 0.7, 'Input 1' => 'Hello world',
                                           'Input 2' => 'Goodbye world' })
      end
    end

    context 'with invalid parameters' do
      context 'when model_bank_ids is missing' do
        it 'raises AppService::Invalid error' do
          invalid_params = valid_params.except(:model_bank_ids)

          expect { service.evaluate(invalid_params) }
            .to raise_error(AppService::Invalid, 'You need to select at least one model')
        end
      end

      context 'when model_bank_ids is empty' do
        it 'raises AppService::Invalid error' do
          invalid_params = valid_params.merge(model_bank_ids: [])

          expect { service.evaluate(invalid_params) }
            .to raise_error(AppService::Invalid, 'You need to select at least one model')
        end
      end

      context 'when prompt is missing' do
        it 'raises AppService::Invalid error' do
          invalid_params = valid_params.except(:prompt)

          expect { service.evaluate(invalid_params) }
            .to raise_error(AppService::Invalid, 'Prompt is required')
        end
      end

      context 'when prompt is empty' do
        it 'raises AppService::Invalid error' do
          invalid_params = valid_params.merge(prompt: '')

          expect { service.evaluate(invalid_params) }
            .to raise_error(AppService::Invalid, 'Prompt is required')
        end
      end

      context 'when params is missing' do
        it 'raises AppService::Invalid error' do
          invalid_params = valid_params.except(:params)
          expect { service.evaluate(invalid_params) }
            .to raise_error(AppService::Invalid, 'Params are required')
        end
      end

      context 'when params is not a hash' do
        it 'raises AppService::Invalid error' do
          invalid_params = valid_params.merge(params: 'not a hash')

          expect { service.evaluate(invalid_params) }
            .to raise_error(AppService::Invalid, 'Params must be a hash')
        end
      end
    end

    context 'with non-existent model bank ids' do
      it 'creates prompt eval but skips non-existent model banks' do
        invalid_params = valid_params.merge(model_bank_ids: [99_999, model_bank_1.id])

        prompt_eval = service.evaluate(invalid_params)

        prompt_eval_results = PromptEvalResult.where(prompt_eval_id: prompt_eval.id)
        expect(prompt_eval_results.count).to eq(1)
        expect(prompt_eval_results.first.model_bank_id).to eq(model_bank_1.id)
      end
    end

    context 'with single model bank' do
      it 'creates prompt eval with single result' do
        single_bank_params = valid_params.merge(model_bank_ids: [model_bank_1.id])

        prompt_eval = service.evaluate(single_bank_params)

        prompt_eval_results = PromptEvalResult.where(prompt_eval_id: prompt_eval.id)
        expect(prompt_eval_results.count).to eq(1)
        expect(prompt_eval_results.first.model_bank_id).to eq(model_bank_1.id)
      end
    end

    context 'with complex params structure' do
      let(:complex_params) do
        {
          model_bank_ids: [model_bank_1.id],
          prompt: 'You are a helpful assistant',
          params: {
            temperature: 0.7,
            'Input 1' => 'Hello world',
            'Input 2' => 'Goodbye world'
          }
        }
      end

      it 'handles complex params correctly' do
        prompt_eval = service.evaluate(complex_params)

        expect(prompt_eval.request_raw['messages']).to include(
          { 'role' => 'system', 'content' => 'You are a helpful assistant' },
          { 'role' => 'user', 'content' => expected_user_message }
        )
      end
    end

    context 'when database transaction fails' do
      before do
        allow(PromptEval).to receive(:create!).and_raise(ActiveRecord::RecordInvalid.new(PromptEval.new))
      end

      it 'rolls back all changes' do
        expect { service.evaluate(valid_params) }
          .to raise_error(ActiveRecord::RecordInvalid)

        expect(PromptEval.count).to eq(1) # Only the existing one
        expect(PromptEvalResult.count).to eq(2) # Only the existing ones
      end
    end
  end

  describe 'private methods' do
    describe '#initialize_request_params' do
      let(:params) do
        {
          prompt: 'You are a helpful assistant',
          params: {
            temperature: 0.7,
            'Input 1' => 'Hello world',
            'Input 2' => 'Goodbye world'
          }
        }
      end

      let(:expected_user_message) do
        <<~USER_MESSAGE
          Input 1: Hello world
          Input 2: Goodbye world
        USER_MESSAGE
      end

      it 'initializes request_raw with correct structure' do
        result = service.send(:initialize_request_params, params)

        expect(result).to include(
          temperature: 0.7,
          messages: [
            { role: 'system', content: 'You are a helpful assistant' },
            { role: 'user', content: expected_user_message }
          ]
        )
      end

      it 'converts params hash to user messages' do
        result = service.send(:initialize_request_params, params)

        expect(result[:messages]).to include(
          { role: 'system', content: 'You are a helpful assistant' },
          { role: 'user', content: expected_user_message }
        )
      end

      it 'sets system_instruction from prompt' do
        result = service.send(:initialize_request_params, params)

        expect(result[:messages][0][:content]).to eq('You are a helpful assistant')
      end

      it 'sets temperature from params' do
        result = service.send(:initialize_request_params, params)

        expect(result[:temperature]).to eq(0.7)
      end
    end
  end
end
