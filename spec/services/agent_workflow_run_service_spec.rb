# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AgentWorkflowRunService, type: :service do
  let(:user) { create(:user) }
  let(:chat) { create(:chat) }
  let(:agent_workflow) { create(:agent_workflow) }
  let!(:agent_node) { create(:agent_workflow_node, agent_workflow: agent_workflow, workflow_type: 'agent') }
  let!(:merger_node) { create(:agent_workflow_node, agent_workflow: agent_workflow, workflow_type: 'merger') }
  let(:model_template) { create(:model_template) }
  let!(:model_template_in) { create(:model_template_in, model_template: model_template) }

  describe '#create' do
    let(:query_list) { { model_template_in.id => 'test query' } }
    let(:params) do
      {
        agent_workflow_id: agent_workflow.id,
        query_list: query_list,
        chat_id: chat.id
      }
    end

    let(:model_template_service) { instance_double(ModelTemplateService) }

    before do
      allow(ModelTemplateService).to receive(:new).with(user).and_return(model_template_service)
      allow(model_template_service).to receive(:build_system_prompt).and_return('system prompt')
      agent_node.update(model_template: model_template)
      allow(AgentWorkflowRunJob).to receive(:perform_later).and_return(true)
    end

    it 'creates an AgentWorkflowRun' do
      expect { described_class.new(user).create(params) }.to change(AgentWorkflowRun, :count).by(1)
    end

    it 'creates AgentWorkflowNodeRuns' do
      expect { described_class.new(user).create(params) }.to change(AgentWorkflowNodeRun, :count).by(2)
    end

    it 'enqueues AgentWorkflowRunJob' do
      described_class.new(user).create(params)
      expect(AgentWorkflowRunJob).to have_received(:perform_later)
    end

    context 'with invalid params' do
      it 'raises an error if agent_workflow_id is invalid' do
        params[:agent_workflow_id] = 'invalid_id'
        expect { described_class.new(user).create(params) }.to raise_error(ActiveRecord::RecordNotFound)
      end

      it 'raises an error if chat_id is invalid' do
        params[:chat_id] = 'invalid_id'
        expect { described_class.new(user).create(params) }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end
end
