# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UserManagementService, '#change_password_request' do
  let(:user) { create(:user, email: '<EMAIL>') }
  let(:organization) { create(:organization) }
  let(:membership) { create(:membership, user: user, organization: organization) }
  let(:service) { described_class.new(user) }
  let(:params) { { email: '<EMAIL>' } }

  before do
    membership # Create the membership
  end

  describe 'POST change password request' do
    context 'when user exists and has organization membership' do
      it 'creates a token request successfully' do
        result = service.change_password_request(params)

        expect(result).to be_a(TokenRequest)
        expect(result.email).to eq('<EMAIL>')
        expect(result.request_status).to eq('requested')
        expect(result.purpose).to eq('change_password')
        expect(result.user_id.to_s).to eq(user.id.to_s)
        expect(result.request_expiry_date).to be_within(1.second).of(Time.current + 10.minutes)
        expect(result.requested_at).to be_within(1.second).of(Time.current)
        expect(result.request_code).to be_present
        expect(result.request_code.length).to eq(128) # 64 hex characters = 128 chars
      end

      it 'enqueues password change request mailer job' do
        expect(Mailer::PasswordChangeRequestMailerJob).to receive(:perform_later)

        service.change_password_request(params)
      end

      it 'downcases the email' do
        result = service.change_password_request({ email: '<EMAIL>' })

        expect(result.email).to eq('<EMAIL>')
      end
    end

    context 'when user does not exist' do
      let(:params) { { email: '<EMAIL>' } }

      it 'raises AppService::Invalid error with invalid email message' do
        expect do
          service.change_password_request(params)
        end.to raise_error(AppService::Invalid, 'Email Invalid')
      end
    end

    context 'when user has no organization membership' do
      before do
        membership.discard
      end

      it 'raises NoMethodError when trying to access organization' do
        expect do
          service.change_password_request(params)
        end.to raise_error(AppService::Invalid, 'You are not in any organization')
      end
    end

    context 'when organization is discarded' do
      before do
        membership.undiscard
        organization.discard!
      end

      it 'raises AppService::Invalid error with organization deleted message' do
        expect do
          service.change_password_request(params)
        end.to raise_error(AppService::Invalid, 'You are not in any organization')
      end
    end

    context 'when user has multiple memberships' do
      let(:organization2) { create(:organization) }
      let!(:membership2) { create(:membership, user: user, organization: organization2) }

      it 'uses the first membership organization' do
        result = service.change_password_request(params)

        expect(result).to be_a(TokenRequest)
        expect(result.user_id.to_s).to eq(user.id.to_s)
      end
    end

    context 'when there is an existing unexpired request' do
      let!(:existing_request) do
        TokenRequest.create!(
          email: '<EMAIL>',
          request_status: 'requested',
          request_code: SecureRandom.hex(64),
          request_expiry_date: Time.current + 5.minutes,
          requested_at: Time.current,
          user_id: user.id,
          purpose: 'change_password'
        )
      end

      it 'raises AppService::Invalid error with already requested message' do
        expect do
          service.change_password_request(params)
        end.to raise_error(AppService::Invalid, 'Already requested change password')
      end
    end

    context 'when there is an expired request' do
      let!(:expired_request) do
        TokenRequest.create!(
          email: '<EMAIL>',
          request_status: 'requested',
          request_code: SecureRandom.hex(64),
          request_expiry_date: Time.current - 1.minute,
          requested_at: Time.current - 11.minutes,
          user_id: user.id,
          purpose: 'change_password'
        )
      end

      it 'allows creating a new request' do
        result = service.change_password_request(params)

        expect(result).to be_a(TokenRequest)
        expect(result.email).to eq('<EMAIL>')
        expect(result.request_status).to eq('requested')
      end
    end

    context 'when there is a confirmed request' do
      let!(:confirmed_request) do
        TokenRequest.create!(
          email: '<EMAIL>',
          request_status: 'confirmed',
          request_code: SecureRandom.hex(64),
          request_expiry_date: Time.current + 5.minutes,
          requested_at: Time.current,
          user_id: user.id,
          purpose: 'change_password'
        )
      end

      it 'allows creating a new request' do
        result = service.change_password_request(params)

        expect(result).to be_a(TokenRequest)
        expect(result.email).to eq('<EMAIL>')
        expect(result.request_status).to eq('requested')
      end
    end

    context 'when request code collision occurs' do
      before do
        allow(SecureRandom).to receive(:hex).and_return('collision_code')

        # Create a request with the collision code
        TokenRequest.create!(
          email: '<EMAIL>',
          request_status: 'requested',
          request_code: 'collision_code',
          request_expiry_date: Time.current + 5.minutes,
          requested_at: Time.current,
          user_id: create(:user).id,
          purpose: 'change_password'
        )
      end

      it 'raises error when code collision occurs' do
        expect do
          service.change_password_request(params)
        end.to raise_error(AppService::Invalid, 'Request failed, please try again')
      end
    end

    context 'when user is platform admin' do
      before do
        membership.update!(role: Membership.role_mappings['super_admin_platform'])
      end

      it 'creates token request successfully' do
        result = service.change_password_request(params)

        expect(result).to be_a(TokenRequest)
        expect(result.email).to eq('<EMAIL>')
        expect(result.request_status).to eq('requested')
      end
    end

    context 'when user is partner admin' do
      before do
        membership.update!(role: Membership.role_mappings['partner_admin'])
      end

      it 'creates token request successfully' do
        result = service.change_password_request(params)

        expect(result).to be_a(TokenRequest)
        expect(result.email).to eq('<EMAIL>')
        expect(result.request_status).to eq('requested')
      end
    end

    context 'with different email formats' do
      it 'handles mixed case email' do
        result = service.change_password_request({ email: '<EMAIL>' })

        expect(result.email).to eq('<EMAIL>')
      end
    end

    context 'when multiple requests are made in quick succession' do
      it 'prevents duplicate requests within expiry window' do
        # First request should succeed
        result1 = service.change_password_request(params)
        expect(result1).to be_a(TokenRequest)

        # Second request should fail
        expect do
          service.change_password_request(params)
        end.to raise_error(AppService::Invalid, 'Already requested change password')
      end
    end
  end
end
