# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TemplateCategoryService, '#index' do
  let(:user) { create(:user) }
  let(:organization) { create(:organization) }
  let(:membership) { create(:membership, user: user, organization: organization) }
  let(:service) { described_class.new(user) }

  before do
    membership # Create the membership
  end

  describe 'GET list template categories' do
    let!(:category1) { create(:template_category, organization: organization, name: 'Category A') }
    let!(:category2) { create(:template_category, organization: organization, name: 'Category B') }
    let!(:other_org_category) do
      create(:template_category, organization: create(:organization), name: 'Other Org Category')
    end

    context 'when user has valid role' do
      before do
        membership.update!(role: Membership.role_mappings['admin'])
      end

      it 'returns categories for user organization' do
        membership.update!(role: Membership.role_mappings['team_admin'])

        result = service.index({})
        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id)
        expect(category_ids).not_to include(other_org_category.id)
      end

      it 'includes used template count' do
        create(:model_template, template_category: category1)
        create(:model_template, template_category: category1)
        create(:model_template, template_category: category2)

        result = service.index({})

        expect(result.used_template_count[category1.id]).to eq(2)
        expect(result.used_template_count[category2.id]).to eq(1)
      end

      context 'with search parameter' do
        it 'filters by search term' do
          result = service.index({ search: 'Category A' })
          category_ids = result.template_categories.map(&:id)
          expect(category_ids).to include(category1.id)
          expect(category_ids).not_to include(category2.id)
        end

        it 'search is case insensitive' do
          result = service.index({ search: 'CATEGORY A' })
          category_ids = result.template_categories.map(&:id)
          expect(category_ids).to include(category1.id)
        end

        it 'search with partial match' do
          result = service.index({ search: 'category' })
          category_ids = result.template_categories.map(&:id)
          expect(category_ids).to include(category1.id, category2.id)
        end

        it 'search returns no results for non-matching term' do
          result = service.index({ search: 'nonexistent' })
          expect(result.template_categories.count).to eq(0)
        end
      end

      context 'with pagination parameters' do
        it 'passes pagination parameters to repository' do
          result = service.index({ page: 1, per_page: 10, disable_pagination: false })
          expect(result.template_categories).to be_present
        end

        it 'handles disable_pagination parameter' do
          result = service.index({ disable_pagination: true })
          expect(result.template_categories).to be_present
        end
      end
    end

    context 'when platform admin' do
      before do
        membership.update!(role: Membership.role_mappings['super_admin_platform'])
      end

      it 'returns all categories when no organization_id specified' do
        result = service.index({})

        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id, other_org_category.id)
      end

      it 'filters by specified organization_id' do
        result = service.index({ organization_id: organization.id })

        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id)
        expect(category_ids).not_to include(other_org_category.id)
      end

      it 'filters by other organization_id' do
        result = service.index({ organization_id: other_org_category.organization_id })

        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(other_org_category.id)
        expect(category_ids).not_to include(category1.id, category2.id)
      end

      it 'combines organization_id with search' do
        result = service.index({
                                 organization_id: organization.id,
                                 search: 'Category A'
                               })

        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id)
        expect(category_ids).not_to include(category2.id, other_org_category.id)
      end
    end

    context 'when user has invalid role' do
      before do
        membership.update!(role: Membership.role_mappings['member'])
      end

      it 'raises unauthorized error' do
        expect do
          service.index({})
        end.to raise_error(ExceptionHandler::Unauthorized)
      end
    end

    context 'when user has no organization' do
      before do
        membership.destroy
      end

      it 'raises unauthorized error' do
        expect do
          service.index({})
        end.to raise_error(ExceptionHandler::Unauthorized)
      end
    end

    context 'with different platform admin roles' do
      it 'works with super_admin_platform role' do
        membership.update!(role: Membership.role_mappings['super_admin_platform'])

        result = service.index({})
        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id, other_org_category.id)
      end

      it 'works with owner_platform role' do
        membership.update!(role: Membership.role_mappings['owner_platform'])

        result = service.index({})
        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id, other_org_category.id)
      end

      it 'works with admin role' do
        membership.update!(role: Membership.role_mappings['admin'])

        result = service.index({})
        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id, other_org_category.id)
      end

      it 'filters by organization_id for admin role' do
        membership.update!(role: Membership.role_mappings['admin'])

        result = service.index({ organization_id: organization.id })
        category_ids = result.template_categories.map(&:id)
        expect(category_ids).to include(category1.id, category2.id)
        expect(category_ids).not_to include(other_org_category.id)
      end
    end

    context 'with empty results' do
      before do
        membership.update!(role: Membership.role_mappings['admin'])
        TemplateCategory.destroy_all
      end

      it 'returns empty result when no categories exist' do
        result = service.index({})

        expect(result.template_categories.count).to eq(0)
        expect(result.used_template_count).to be_empty
      end
    end

    context 'with multiple organizations' do
      let(:organization2) { create(:organization) }
      let(:category3) { create(:template_category, organization: organization2, name: 'Category C') }

      before do
        membership.update!(role: Membership.role_mappings['super_admin_platform'])
        category3 # Create the category
      end

      it 'can filter by multiple different organizations' do
        result1 = service.index({ organization_id: organization.id })
        result2 = service.index({ organization_id: organization2.id })

        category_ids1 = result1.template_categories.map(&:id)
        category_ids2 = result2.template_categories.map(&:id)

        expect(category_ids1).to include(category1.id, category2.id)
        expect(category_ids1).not_to include(category3.id)

        expect(category_ids2).to include(category3.id)
        expect(category_ids2).not_to include(category1.id, category2.id)
      end
    end
  end
end
