# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::ModelBanks#index', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:model_bank_1) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:model_bank_2) do
    create(:model_bank, name: 'GPT-4o Mini', code: 'gpt-4o-mini', input_rate: 0.0003, output_rate: 0.0012)
  end

  def get_model_banks(params = {}, which_user = user)
    get '/v1/model_banks', params, as_user(which_user)
  end

  describe 'GET /v1/model_banks' do
    context 'with valid user' do
      it 'returns list of model banks' do
        get_model_banks({}, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
        expect(response_data.first['name']).to eq('GPT-4o')
        expect(response_data.first['code']).to eq('gpt-4o')
        expect(response_data.first['input_rate']).to eq(0.01)
        expect(response_data.first['output_rate']).to eq(0.03)
      end

      it 'returns model banks with correct structure' do
        get_model_banks({}, user)
        expect_response(:ok)

        model_bank = response_data.first
        expect(model_bank).to include(
          'id',
          'name',
          'code',
          'input_rate',
          'output_rate',
          'web_search_rate',
          'image_rate',
          'file_rate',
          'status'
        )
      end
    end

    context 'with query parameters' do
      it 'filters by status' do
        create(:model_bank, status: 'inactive')

        get_model_banks({ status: 'active' }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
        expect(response_data.map { |bank| bank['status'] }).to all(eq('active'))
      end

      it 'handles pagination' do
        get_model_banks({ page: 1, per_page: 1 }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(1)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/model_banks', {}, {}
        expect_response(:unauthorized)
      end
    end
  end
end
