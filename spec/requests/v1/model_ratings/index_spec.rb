# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ModelRatingsController, type: :request do
  let!(:organization) { create(:organization) }
  let!(:admin_platform) { create(:user) }
  let!(:membership_admin_platform) do
    create(:membership, :admin, user: admin_platform, organization:)
  end
  let!(:owner_organization) { create(:user) }
  let!(:membership_owner_organization) do
    create(:membership, :owner, user: owner_organization, organization:)
  end
  let!(:list_categories) { create_list(:template_category, 2, organization:) }
  let!(:model_template) do
    create(:model_template, organization:, user: owner_organization, template_category: list_categories.first)
  end
  let!(:model_template2) do
    create(:model_template, organization:, user: owner_organization, template_category: list_categories.second)
  end
  let!(:model) { create(:model, model_template:) }
  let!(:model2) { create(:model, model_template: model_template2) }
  let!(:workspace) { create(:workspace, organization:) }
  let!(:chat) { create(:chat, workspace:, model:) }
  let!(:message) { create(:message, chat:, sender: 'user', content: 'Test Message', model_used: 'openai/gpt-4o-mini') }
  let!(:rating) { create(:model_rating, user: owner_organization, model_template:, message:) }
  let!(:chat2) { create(:chat, workspace:, model: model2) }
  let!(:message2) do
    create(:message, chat: chat2, sender: 'user', content: 'Test Message', model_used: 'openai/gpt-4o')
  end
  let!(:rating2) { create(:model_rating, user: owner_organization, model_template: model_template2, message: message2) }

  let!(:other_organization) { create(:organization) }
  let!(:owner_other_organization) { create(:user) }
  let!(:membership_owner_other_organization) do
    create(:membership, :owner, user: owner_other_organization, organization: other_organization)
  end
  let!(:list_categories_other_organization) { create_list(:template_category, 2, organization: other_organization) }
  let!(:model_template_other_organization) do
    create(:model_template, organization: other_organization, user: owner_other_organization)
  end
  let!(:model_other_organization) { create(:model, model_template: model_template_other_organization) }
  let!(:chat_other_organization) { create(:chat, workspace: workspace, model: model_other_organization) }
  let!(:message_other_organization) do
    create(:message, chat: chat_other_organization, sender: 'user', content: 'Test Message',
                     model_used: 'openai/gpt-4o')
  end
  let!(:rating_other_organization) do
    create(:model_rating, user: owner_other_organization, model_template: model_template_other_organization,
                          message: message_other_organization)
  end

  def index_model_ratings(params, user)
    get '/v1/model_ratings', params, as_user(user)
  end

  describe 'GET list model ratings' do
    context 'when user is platform admin' do
      it 'return list model ratings from all organizations' do
        index_model_ratings({}, admin_platform)
        expect_response(:ok)

        expect(response_data.size).to eq 3
      end
    end

    context 'when user is owner' do
      it 'return list model ratings from same organization' do
        index_model_ratings({ organization_id: organization.id }, owner_organization)
        expect_response(:ok)
        expect(response_data.size).to eq 2
      end
    end

    context 'when user is owner organizations' do
      it 'return list model ratings from same organization' do
        index_model_ratings({ organization_id: organization.id }, owner_other_organization)
        expect_response(:ok)
        expect(response_data.size).to eq 1
      end
    end

    context 'when user is not platform admin and not owner' do
      let!(:user) { create(:user) }
      let!(:membership_user) { create(:membership, :member, user:, organization:) }

      it 'return empty list' do
        index_model_ratings({}, user)
        expect_response(:ok)
        expect(response_data.size).to eq 0
      end
    end

    it 'returns with correct output' do
      index_model_ratings({}, admin_platform)
      expect_response(
        :ok,
        data: [
          {
            id: Integer,
            rating: Integer,
            comment: String,
            commented_at: String,
            model: String,
            number_of_used_times: Integer,
            model_template: {
              id: Integer,
              name: String,
              category: {
                id: Integer,
                name: String
              }
            },
            user: {
              id: Integer,
              name: String,
              role: String
            }
          }
        ]
      )
    end

    it "return filtered list from same organization with 'model_template_id' params" do
      index_model_ratings({ model_template_id: -1 }, admin_platform)
      expect_response(:ok)
      expect(response_data.size).to eq 0
    end

    it "return filtered list from same organization with 'user_id' params" do
      index_model_ratings({ user_id: owner_organization.id }, admin_platform)
      expect_response(:ok)

      expect(response_data.size).to eq 2
      expect(response_data.first['user']['id']).to eq owner_organization.id
    end

    it 'return only selected category' do
      index_model_ratings({ template_category_id: [list_categories.first.id] }, admin_platform)
      expect_response(:ok)

      expect(response_data.size).to eq 1
      expect(response_data.first['model_template']['category']['name']).to eq list_categories.first.name
    end

    it 'return only similar model template name' do
      index_model_ratings({ model_template_name: model_template.name }, admin_platform)
      expect_response(:ok)

      expect(response_data.size).to eq 1
      expect(response_data.first['model_template']['name']).to eq model_template.name
    end
  end
end
