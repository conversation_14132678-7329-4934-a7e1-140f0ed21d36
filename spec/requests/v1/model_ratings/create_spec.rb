# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ModelRatingsController, type: :request do
  let!(:organization) { create(:organization) }
  let!(:admin_platform) { create(:user) }
  let!(:membership_admin_platform) do
    create(:membership, :admin, user: admin_platform, organization:)
  end

  let!(:user) { create(:user) }
  let!(:membership_user) do
    create(:membership, user:, organization:)
  end

  let!(:workspace) { create(:workspace, organization:) }
  let!(:chat) { create(:chat, workspace:) }
  let!(:message) do
    create(:message, chat:, sender: 'assistant', content: 'Test Message', model_used: 'openai/gpt-4o-mini')
  end

  let!(:model_template) { create(:model_template, organization:, user:) }
  let!(:model) { create(:model, model_template:) }

  let!(:model_rating_creation_input) { { chat_id: chat.id, model_template_id: model_template.id, rating: 5 } }

  def rate(params, as_user = user)
    post '/v1/model_ratings', params, as_user(as_user)
  end

  describe 'POST /v1/model_ratings' do
    context 'when the user is not authenticated' do
      it 'returns a 401 status code' do
        post '/v1/model_ratings', model_rating_creation_input
        expect_response(:unauthorized)
      end
    end

    context 'when the user is authenticated' do
      it 'returns a 201 status code' do
        rate(model_rating_creation_input)
        expect_response(:created)
      end
    end
  end

  it 'returns created and data to create a model rating' do
    rate(model_rating_creation_input)
    expect_response(:created)

    id = response_data['id']
    model_rating = ModelRating.find(id)
    expect(model_rating.message_id).to eq(message.id)
    expect(model_rating.model_template_id).to eq(model_template.id)
    expect(model_rating.rating).to eq(5)
    expect(model_rating.user_id.to_s).to eq(user.id.to_s)
    expect(model_rating.comment).to be_nil
    expect(model_rating.feedback).to be_nil
  end
end
