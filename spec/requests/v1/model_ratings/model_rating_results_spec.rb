# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/model_ratings/results', type: :request do
  let!(:organization) { create(:organization) }
  let!(:admin_platform) { create(:user) }
  let!(:membership_admin_platform) do
    create(:membership, :admin, user: admin_platform, organization: organization)
  end
  let!(:owner_organization) { create(:user) }
  let!(:membership_owner_organization) do
    create(:membership, :owner, user: owner_organization, organization: organization)
  end
  let!(:regular_user) { create(:user) }
  let!(:membership_regular_user) do
    create(:membership, user: regular_user, organization: organization)
  end
  let!(:template_category) { create(:template_category, organization: organization) }
  let!(:model_template) do
    create(:model_template,
           organization: organization,
           user: owner_organization,
           template_category: template_category,
           name: 'Marketing Assistant')
  end
  let!(:model) { create(:model, model_template: model_template) }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:chat) { create(:chat, workspace: workspace, model: model) }
  let!(:message) do
    create(:message,
           chat: chat,
           sender: 'user',
           content: 'Test marketing message',
           model_used: 'openai/gpt-4o-mini')
  end
  let!(:model_rating) do
    create(:model_rating,
           user: owner_organization,
           model_template: model_template,
           message: message,
           rating: 5,
           comment: 'Excellent marketing assistant!')
  end

  def get_model_rating_results(params = {}, which_user = admin_platform)
    get '/v1/model_ratings/results.csv', params, as_user(which_user).merge('Accept' => 'text/csv')
  end

  describe 'GET /v1/model_ratings/results.csv' do
    context 'when user is platform admin' do
      it 'returns CSV file with correct headers and content' do
        get_model_rating_results({}, admin_platform)
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('text/csv')
        expect(response.headers['Content-Disposition']).to include('attachment')
        expect(response.headers['Content-Disposition']).to include('agent_analytics_results_')
        expect(response.headers['Content-Disposition']).to include('.csv')
        
        csv_content = response.body
        expect(csv_content).to include('Agent Name,Agent Category,Rating,Organization,User,Role,Rating,Model Used,Comment,Date Commented')
        expect(csv_content).to include('Marketing Assistant')
        expect(csv_content).to include(organization.name)
        expect(csv_content).to include(owner_organization.display_name)
        expect(csv_content).to include('5')
        expect(csv_content).to include('Excellent marketing assistant!')
      end

      it 'generates filename with timestamp' do
        freeze_time = Time.parse('2024-01-15 14:30:45')
        allow(Time).to receive(:now).and_return(freeze_time)
        
        get_model_rating_results({}, admin_platform)
        
        expected_filename = 'agent_analytics_results_20240115143045.csv'
        expect(response.headers['Content-Disposition']).to include(expected_filename)
      end

      it 'includes template category information' do
        get_model_rating_results({}, admin_platform)
        
        csv_content = response.body
        expect(csv_content).to include(template_category.name)
      end
    end

    context 'when user is organization owner' do
      it 'returns CSV file with organization data' do
        get_model_rating_results({}, owner_organization)
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('text/csv')
        
        csv_content = response.body
        expect(csv_content).to include('Marketing Assistant')
        expect(csv_content).to include(organization.name)
      end
    end

    context 'when user is regular member' do
      it 'returns empty CSV with only headers' do
        get_model_rating_results({}, regular_user)
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('text/csv')
        
        csv_content = response.body
        expect(csv_content).to include('Agent Name,Agent Category,Rating,Organization,User,Role,Rating,Model Used,Comment,Date Commented')
        expect(csv_content).not_to include('Marketing Assistant')
      end
    end

    context 'when filtering by model_template_id' do
      let!(:other_template) do
        create(:model_template,
               organization: organization,
               user: owner_organization,
               name: 'Sales Assistant')
      end
      let!(:other_model) { create(:model, model_template: other_template) }
      let!(:other_chat) { create(:chat, workspace: workspace, model: other_model) }
      let!(:other_message) { create(:message, chat: other_chat) }
      let!(:other_rating) do
        create(:model_rating,
               user: owner_organization,
               model_template: other_template,
               message: other_message)
      end

      it 'returns only filtered template data' do
        get_model_rating_results({ model_template_id: model_template.id }, admin_platform)
        
        csv_content = response.body
        expect(csv_content).to include('Marketing Assistant')
        expect(csv_content).not_to include('Sales Assistant')
      end
    end

    context 'when filtering by organization_id' do
      let!(:other_organization) { create(:organization) }
      let!(:other_template) do
        create(:model_template,
               organization: other_organization,
               name: 'Other Org Template')
      end

      it 'returns only specified organization data' do
        get_model_rating_results({ organization_id: organization.id }, admin_platform)
        
        csv_content = response.body
        expect(csv_content).to include(organization.name)
        expect(csv_content).not_to include(other_organization.name)
      end
    end

    context 'when filtering by user_id' do
      let!(:other_user) { create(:user) }
      let!(:other_membership) { create(:membership, user: other_user, organization: organization) }
      let!(:other_rating) do
        create(:model_rating,
               user: other_user,
               model_template: model_template,
               message: message)
      end

      it 'returns only specified user data' do
        get_model_rating_results({ user_id: owner_organization.id }, admin_platform)
        
        csv_content = response.body
        expect(csv_content).to include(owner_organization.display_name)
        expect(csv_content).not_to include(other_user.display_name)
      end
    end

    context 'when no ratings exist' do
      before do
        ModelRating.destroy_all
      end

      it 'returns CSV with only headers' do
        get_model_rating_results({}, admin_platform)
        
        expect(response).to have_http_status(:ok)
        csv_content = response.body
        expect(csv_content).to eq("Agent Name,Agent Category,Rating,Organization,User,Role,Rating,Model Used,Comment,Date Commented\n")
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get '/v1/model_ratings/results.csv', {}
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with multiple ratings and complex data' do
      let!(:template_category_2) { create(:template_category, organization: organization, name: 'Sales Category') }
      let!(:model_template_2) do
        create(:model_template,
               organization: organization,
               user: owner_organization,
               template_category: template_category_2,
               name: 'Sales Assistant')
      end
      let!(:model_2) { create(:model, model_template: model_template_2) }
      let!(:chat_2) { create(:chat, workspace: workspace, model: model_2) }
      let!(:message_2) { create(:message, chat: chat_2, model_used: 'openai/gpt-4o') }
      let!(:rating_2) do
        create(:model_rating,
               user: owner_organization,
               model_template: model_template_2,
               message: message_2,
               rating: 3,
               comment: 'Good but needs improvement')
      end

      it 'includes all ratings in CSV' do
        get_model_rating_results({}, admin_platform)
        
        csv_content = response.body
        expect(csv_content).to include('Marketing Assistant')
        expect(csv_content).to include('Sales Assistant')
        expect(csv_content).to include('Excellent marketing assistant!')
        expect(csv_content).to include('Good but needs improvement')
        expect(csv_content).to include('openai/gpt-4o-mini')
        expect(csv_content).to include('openai/gpt-4o')
      end
    end
  end
end
