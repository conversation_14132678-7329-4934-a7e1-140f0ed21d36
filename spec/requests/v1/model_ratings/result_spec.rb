# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ModelRatingsController, type: :request do
  let!(:organization) { create(:organization) }
  let!(:admin_platform) { create(:user) }
  let!(:membership_admin_platform) do
    create(:membership, :admin, user: admin_platform, organization:)
  end
  let!(:owner_organization) { create(:user) }
  let!(:membership_owner_organization) do
    create(:membership, :owner, user: owner_organization, organization:)
  end
  let!(:list_categories) { create_list(:template_category, 2, organization:) }
  let!(:model_template) do
    create(:model_template, organization:, user: owner_organization, template_category: list_categories.first)
  end
  let!(:model_template2) do
    create(:model_template, organization:, user: owner_organization, template_category: list_categories.second)
  end
  let!(:model) { create(:model, model_template:) }
  let!(:model2) { create(:model, model_template: model_template2) }
  let!(:workspace) { create(:workspace, organization:) }
  let!(:chat) { create(:chat, workspace:, model:) }
  let!(:message) { create(:message, chat:, sender: 'user', content: 'Test Message', model_used: 'openai/gpt-4o-mini') }
  let!(:rating) { create(:model_rating, user: owner_organization, model_template:, message:) }
  let!(:chat2) { create(:chat, workspace:, model: model2) }
  let!(:message2) do
    create(:message, chat: chat2, sender: 'user', content: 'Test Message', model_used: 'openai/gpt-4o')
  end
  let!(:rating2) { create(:model_rating, user: owner_organization, model_template: model_template2, message: message2) }

  let!(:other_organization) { create(:organization) }
  let!(:owner_other_organization) { create(:user) }
  let!(:membership_owner_other_organization) do
    create(:membership, :owner, user: owner_other_organization, organization: other_organization)
  end
  let!(:list_categories_other_organization) { create_list(:template_category, 2, organization: other_organization) }
  let!(:model_template_other_organization) do
    create(:model_template, organization: other_organization, user: owner_other_organization)
  end
  let!(:model_other_organization) { create(:model, model_template: model_template_other_organization) }
  let!(:chat_other_organization) { create(:chat, workspace: workspace, model: model_other_organization) }
  let!(:message_other_organization) do
    create(:message, chat: chat_other_organization, sender: 'user', content: 'Test Message',
                     model_used: 'openai/gpt-4o')
  end
  let!(:rating_other_organization) do
    create(:model_rating, user: owner_other_organization, model_template: model_template_other_organization,
                          message: message_other_organization)
  end

  def get_model_rating_results(params, user)
    get '/v1/model_ratings/results.csv', params, as_user(user)
  end

  describe 'GET model rating results' do
    context 'when user is platform admin' do
      it 'returns a 200 status code' do
        get_model_rating_results({}, admin_platform)
        expect_response(:ok)
      end
    end

    context 'when user is owner' do
      it 'returns a 200 status code' do
        get_model_rating_results({}, owner_organization)
        expect_response(:ok)
      end
    end

    context 'when user is not platform admin' do
      let!(:user) { create(:user) }
      let!(:membership_user) do
        create(:membership, user:, organization:)
      end

      it 'returns empty csv file with only headers' do
        get_model_rating_results({}, user)
        expect(response.headers['Content-Type']).to eq('text/csv')
        expect(response.headers['Content-Disposition']).to include("attachment; filename=\"agent_analytics_results_#{Time.now.strftime('%Y%m%d%H%M%S')}.csv\"")
        expect(response.body).to include('Agent Name,Agent Category,Rating,Organization,User,Role,Rating,Model Used,Comment,Date Commented')
      end
    end

    it 'returns a csv file' do
      get_model_rating_results({}, admin_platform)
      expect(response.headers['Content-Type']).to eq('text/csv')
      expect(response.headers['Content-Disposition']).to include("attachment; filename=\"agent_analytics_results_#{Time.now.strftime('%Y%m%d%H%M%S')}.csv\"")
      expect(response.body).to include('Agent Name,Agent Category,Rating,Organization,User,Role,Rating,Model Used,Comment,Date Commented')
      expect(response.body).to include(model_template.name)
      expect(response.body).to include(model_template2.name)
      expect(response.body).to include(organization.name)

      expect(response.body).to include(owner_organization.display_name)
      expect(response.body).to include(owner_other_organization.display_name)
      expect(response.body).to include(rating.rating.to_s)
    end

    it 'returns only filtered results' do
      get_model_rating_results({ model_template_id: model_template.id }, admin_platform)
      expect(response.body).to include(model_template.name)
      expect(response.body).not_to include(model_template2.name)
    end
  end
end
