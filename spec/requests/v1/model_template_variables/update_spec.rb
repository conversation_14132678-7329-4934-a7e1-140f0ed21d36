# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::ModelTemplateVariables#update', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test Admin',
      email: '<EMAIL>',
      password: 'password'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Test Member',
      email: '<EMAIL>',
      password: 'password'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization')
  end

  let!(:membership_admin) do
    Membership.create!(user: admin, organization: organization, role: 'admin')
  end

  let!(:membership_member) do
    Membership.create!(user: member, organization: organization, role: 'member')
  end

  let!(:model_template) do
    ModelTemplate.create!(
      name: 'Test Model Template',
      organization:,
      user: admin,
      prompt: 'Test prompt',
      model: 'openai/gpt-4o',
      max_tokens: 1000,
      instruction: 'Test instruction',
      temperature: 0.7,
      template_type: 'default',
      verified: true,
      description: 'This is a test model template'
    )
  end

  let!(:model_template_variable) do
    ModelTemplateVariable.create!(
      name: 'Test Variable',
      model_template: model_template
    )
  end

  let!(:model) do
    Model.create!(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098',
      model_template: model_template
    )
  end

  let(:valid_params) do
    {
      name: 'Updated Variable'
    }
  end

  def update_variable(variable_id, params, user = admin)
    put "/v1/model_template_variables/#{variable_id}", params, as_user(user)
  end

  describe 'PUT update model template variable' do
    context 'when variable_reference_url is provided' do
      let(:valid_params) do
        {
          name: 'Updated Variable',
          variable_reference_url: 'http://example.com/updated_variable'
        }
      end

      it 'updates the model template variable' do
        allow(External::Ragie).to receive(:new).and_return(double(create_document_from_url: { 'id' => 'doc_123' }))

        update_variable(model_template_variable.id, valid_params, admin)
        expect_response(:ok)

        openai_file = OpenaiFile.find_by(
          object_id: model_template_variable.id,
          object_class: model_template_variable.class.name,
          object_class_column: 'variable_reference_url'
        )

        expect(openai_file).to be_present
        expect(openai_file.openai_file_id).to eq('doc_123') # Assuming the mock returns this ID
        expect(model_template_variable.reload.variable_reference_url).to eq('http://example.com/updated_variable')
      end

      it 'updated new variable_reference_url' do
        allow(External::Ragie).to receive(:new).and_return(double(create_document_from_url: { 'id' => 'doc_123' },
                                                                  delete_document: true))

        openai_file = OpenaiFile.create!(
          object_id: model_template_variable.id,
          object_class: model_template_variable.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: 'existing_doc_123'
        )

        update_variable(model_template_variable.id, valid_params, admin)
        expect_response(:ok)

        openai_file.reload
        expect(openai_file.discarded?).to be true

        new_openai_file = OpenaiFile.find_by(
          object_id: model_template_variable.id,
          object_class: model_template_variable.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: 'doc_123'
        )

        expect(new_openai_file).to be_present
        expect(new_openai_file.openai_file_id).to eq('doc_123')
      end

      it 'discard previous OpenAI file if variable_reference_url is blank' do
        allow(External::Ragie).to receive(:new).and_return(double(delete_document: { 'id' => 'doc_123' }))

        openai_file = OpenaiFile.create!(
          object_id: model_template_variable.id,
          object_class: model_template_variable.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: 'existing_doc_123'
        )

        valid_params[:variable_reference_url] = nil
        update_variable(model_template_variable.id, valid_params, admin)
        expect_response(:ok)

        expect(openai_file.reload.discarded?).to be true
      end
    end

    it 'updates the model template variable without variable_reference_url' do
      update_variable(model_template_variable.id, valid_params, admin)
      expect_response(:ok)

      model_template_variable.reload
      expect(model_template_variable.name).to eq('Updated Variable')
    end
  end
end
