# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::ModelTemplateVariables#create', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test Admin',
      email: '<EMAIL>',
      password: 'password'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Test Member',
      email: '<EMAIL>',
      password: 'password'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization')
  end

  let!(:membership_admin) do
    Membership.create!(user: admin, organization: organization, role: 'admin')
  end

  let!(:membership_member) do
    Membership.create!(user: member, organization: organization, role: 'member')
  end

  let!(:model_template) do
    ModelTemplate.create!(
      name: 'Test Model Template',
      organization: organization,
      user: admin,
      prompt: 'Test prompt',
      model: 'openai/gpt-4o',
      max_tokens: 1000,
      instruction: 'Test instruction',
      temperature: 0.7,
      template_type: 'default',
      verified: true,
      description: 'This is a test model template'
    )
  end

  let!(:model) do
    Model.create!(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098',
      model_template: model_template
    )
  end

  let(:valid_params) do
    {
      model_template_id: model_template.id,
      name: 'Test Variable'
    }
  end

  def create(params, which_user = admin)
    post '/v1/model_template_variables', params, as_user(which_user)
  end

  describe 'POST create model template variable' do
    context 'when invalid params' do
      it 'returns 422 with errors' do
        create({}, admin)
        expect_response(:unprocessable_entity)
      end
    end

    context 'when variable_reference_url is provided' do
      let(:valid_params) do
        {
          model_template_id: model_template.id,
          name: 'Test Variable',
          variable_reference_url: 'http://example.com/variable'
        }
      end

      it 'creates a model template variable' do
        allow(External::Ragie).to receive(:new).and_return(double(create_document_from_url: { 'id' => 'doc_123' }))

        create(valid_params, admin)
        expect_response(:created)

        expect(ModelTemplateVariable.count).to eq(1)
        variable = ModelTemplateVariable.last

        openai_file = OpenaiFile.find_by(
          object_id: variable.id,
          object_class: variable.class.name,
          object_class_column: 'variable_reference_url'
        )

        expect(openai_file).to be_present
        expect(openai_file.openai_file_id).to eq('doc_123')
      end
    end

    it 'creates a model template variable without variable_reference_url' do
      create(valid_params, admin)
      expect_response(:created)

      expect(ModelTemplateVariable.count).to eq(1)
      variable = ModelTemplateVariable.last

      expect(variable.name).to eq(valid_params[:name])
      expect(variable.model_template_id).to eq(model_template.id)

      openai_file = OpenaiFile.find_by(
        object_id: variable.id,
        object_class: variable.class.name,
        object_class_column: 'variable_reference_url'
      )

      expect(openai_file).to be_nil
    end
  end
end
