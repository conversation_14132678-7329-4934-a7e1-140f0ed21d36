# frozen_string_literal: true

require 'request_rails_helper'

RSpec.describe 'V1::PlatformRoles::Admins', type: :request do
  let!(:owner) { create(:user) }
  let!(:super_admin) { create(:user) }
  let!(:admin) { create(:user) }
  let(:organization) { create(:organization) }
  let!(:owner_membership) do
    create(:membership, user: owner, organization:, role: Membership.role_mappings['owner_platform'])
  end
  let!(:admin_membership) { create(:membership, user: admin, organization:, role: Membership.role_mappings['admin']) }
  let!(:super_admin_membership) do
    create(:membership, user: super_admin, organization:, role: Membership.role_mappings['super_admin_platform'])
  end

  def platform_roles(which_user = owner)
    get '/v1/platform_roles/admins', {}, as_user(which_user)
  end

  describe 'GET /v1/platform_roles' do
    context 'when user is owner_platform' do
      it 'returns a list of platform roles' do
        platform_roles(owner)
        expect_response(
          :ok,
          data: [
            {
              id: Integer,
              role: String,
              email: String,
              user: {
                name: String,
                display_name: String
              },
              organization: {
                id: Integer,
                name: String,
                code: String
              }
            }
          ]
        )
      end
    end

    context 'when user is super_admin_platform' do
      it 'returns a list of super_admin_platform and admin roles' do
        platform_roles(super_admin)
        expect_response(:ok)

        roles = response_data.map { |d| d['role'] }
        expect(roles).to contain_exactly('super_admin_platform', 'admin')
      end
    end

    context 'when user is admin' do
      it 'returns a list of admin roles' do
        platform_roles(admin)
        expect_response(:ok)

        roles = response_data.map { |d| d['role'] }
        expect(roles).to all(eq('admin'))
      end
    end

    context 'when partner_admin present' do
      let!(:partner_admin) { create(:user) }
      let!(:partner_admin_membership) do
        create(:membership, user: partner_admin, organization:, role: Membership.role_mappings['partner_admin'])
      end

      it 'returns a list of partner_admin roles' do
        platform_roles(owner)
        expect_response(:ok)

        roles = response_data.map { |d| d['role'] }
        expect(roles).to include('partner_admin')
      end

      it 'returns a list of partner_admin with its managed organizations' do
        org1 = create(:organization, created_by_id: partner_admin.id)
        org2 = create(:organization, created_by_id: partner_admin.id)
        platform_roles(owner)
        expect_response(:ok)

        expected_user = response_data.find { |d| d['email'] == partner_admin.email }
        expect(expected_user['managed_organizations']).to match_array(
          [
            {
              id: org1.id,
              name: org1.name,
              code: org1.code
            },
            {
              id: org2.id,
              name: org2.name,
              code: org2.code
            }
          ]
        )
      end
    end

    context 'when user is not authorized' do
      let!(:user) { create(:user) }
      let!(:membership) { create(:membership, user:, organization:, role: Membership.role_mappings['user']) }

      it 'returns forbidden' do
        platform_roles(user)
        expect_response(:forbidden)
      end
    end
  end
end
