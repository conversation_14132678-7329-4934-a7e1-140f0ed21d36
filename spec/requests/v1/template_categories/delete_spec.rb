# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::TemplateCategories#destroy', type: :request do
  let!(:admin) { create(:user) }
  let!(:member) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:organization2) { create(:organization) }
  let!(:membership_admin) { create(:membership, user: admin, organization: organization, role: 'admin') }
  let!(:membership_member) { create(:membership, user: member, organization: organization, role: 'member') }
  let!(:template_category) { create(:template_category, organization: organization) }
  let!(:template_category2) { create(:template_category, organization: organization) }
  let!(:model_template) { create(:model_template, template_category: template_category) }
  let!(:model_template2) { create(:model_template, template_category: template_category) }

  def destroy(id, params, which_user = admin)
    delete "/v1/template_categories/#{id}", params, as_user(which_user)
  end

  describe 'DELETE template category' do
    context 'when not from the same organization' do
      before do
        organization2
        template_category.update!(organization_id: organization2.id)
      end

      it 'return unauthorized' do
        destroy(template_category.id, {}, admin)
        expect_response(:forbidden)
      end
    end

    it 'update model template template category id when new_template_category_id is provided' do
      destroy(template_category.id, { new_template_category_id: template_category2.id }, admin)
      expect_response(:ok)

      model_template.reload
      expect(model_template.template_category_id).to eq(template_category2.id)

      model_template2.reload
      expect(model_template2.template_category_id).to eq(template_category2.id)
    end

    it 'update model template template category id to nil when new_template_category_id is not provided' do
      destroy(template_category.id, {}, admin)
      expect_response(:ok)

      model_template.reload
      expect(model_template.template_category_id).to be_nil

      model_template2.reload
      expect(model_template2.template_category_id).to be_nil
    end
  end
end
