# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::TemplateCategoriesController, type: :request do
  let!(:test_user) { create(:user, display_name: 'Test', email: '<EMAIL>') }
  let!(:test_user2) { create(:user, display_name: 'Test2', email: '<EMAIL>') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:membership_test_user) { create(:membership, :team_admin, user: test_user, organization: organization) }

  let!(:test_user_org2) { create(:user, display_name: 'Test Org2', email: '<EMAIL>') }
  let!(:organization2) { create(:organization, name: 'Test2') }
  let!(:membership_test_user_org2) { create(:membership, user: test_user_org2, organization: organization2) }

  let!(:template_category1) { create(:template_category, organization: organization, name: 'Category A') }
  let!(:template_category2) { create(:template_category, organization: organization, name: 'Category B') }
  let!(:template_category_org2) { create(:template_category, organization: organization2, name: 'Other Org Category') }

  def index_template_categories(params, user)
    get '/v1/template_categories', params, as_user(user)
  end

  describe 'GET list template categories' do
    it 'return list template categories from same organization' do
      expect(TemplateCategory.all.count).to eq 3

      index_template_categories({}, test_user)
      expect_response(:ok)

      expect(response_data.size).to eq 2

      # Verify categories belong to user's organization
      category_ids = response_data.map { |c| c[:id] }
      expect(category_ids).to include(template_category1.id, template_category2.id)
      expect(category_ids).not_to include(template_category_org2.id)
    end

    context 'with model templates present' do
      let!(:model_template1) do
        create(:model_template, organization: organization, user: test_user, template_category: template_category1)
      end

      let!(:model_template2) do
        create(:model_template, organization: organization, user: test_user, template_category: template_category1)
      end

      let!(:model_template3) do
        create(:model_template, organization: organization, user: test_user, template_category: template_category2)
      end

      it 'return list template categories with used_template_count' do
        index_template_categories({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 2

        category1_response = response_data.find { |c| c[:id] == template_category1.id }
        category2_response = response_data.find { |c| c[:id] == template_category2.id }

        expect(category1_response[:used_template_count]).to eq 2
        expect(category2_response[:used_template_count]).to eq 1
      end
    end

    context 'with search functionality' do
      let!(:searchable_category) { create(:template_category, organization: organization, name: 'Marketing Category') }
      let!(:another_category) { create(:template_category, organization: organization, name: 'Sales Category') }

      it 'search by name' do
        index_template_categories({ search: 'Marketing' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first[:name]).to eq 'Marketing Category'
      end

      it 'search is case insensitive' do
        index_template_categories({ search: 'MARKETING' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first[:name]).to eq 'Marketing Category'
      end

      it 'search with partial match' do
        index_template_categories({ search: 'market' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first[:name]).to eq 'Marketing Category'
      end

      it 'search returns no results for non-matching term' do
        index_template_categories({ search: 'nonexistent' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 0
      end
    end

    context 'with pagination parameters' do
      it 'handles pagination parameters' do
        index_template_categories({ page: 1, per_page: 10 }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 2
      end

      it 'handles disable_pagination parameter' do
        index_template_categories({ disable_pagination: true }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 2
      end
    end

    context 'when user has admin role' do
      before do
        membership_test_user.update!(role: Membership.role_mappings['admin'])
      end

      it 'returns all categories when no organization_id specified' do
        index_template_categories({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 3

        category_ids = response_data.map { |c| c[:id] }
        expect(category_ids).to include(template_category1.id, template_category2.id, template_category_org2.id)
      end

      it 'filters by specified organization_id' do
        index_template_categories({ organization_id: organization.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 2

        category_ids = response_data.map { |c| c[:id] }
        expect(category_ids).to include(template_category1.id, template_category2.id)
        expect(category_ids).not_to include(template_category_org2.id)
      end

      it 'filters by other organization_id' do
        index_template_categories({ organization_id: organization2.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1

        category_ids = response_data.map { |c| c[:id] }
        expect(category_ids).to include(template_category_org2.id)
        expect(category_ids).not_to include(template_category1.id, template_category2.id)
      end

      it 'combines organization_id with search' do
        index_template_categories({
                                    organization_id: organization.id,
                                    search: 'Category A'
                                  }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first[:name]).to eq 'Category A'
      end
    end

    context 'when user has platform admin role' do
      before do
        membership_test_user.update!(role: Membership.role_mappings['super_admin_platform'])
      end

      it 'returns all categories when no organization_id specified' do
        index_template_categories({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 3

        category_ids = response_data.map { |c| c[:id] }
        expect(category_ids).to include(template_category1.id, template_category2.id, template_category_org2.id)
      end

      it 'filters by specified organization_id' do
        index_template_categories({ organization_id: organization.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 2

        category_ids = response_data.map { |c| c[:id] }
        expect(category_ids).to include(template_category1.id, template_category2.id)
        expect(category_ids).not_to include(template_category_org2.id)
      end
    end

    context 'when user has invalid role' do
      before do
        membership_test_user.update!(role: Membership.role_mappings['member'])
      end

      it 'raises forbidden error' do
        index_template_categories({}, test_user)
        expect_error_response(:forbidden)
      end
    end

    context 'when user has no organization' do
      before do
        membership_test_user.destroy
      end

      it 'raises forbidden error' do
        index_template_categories({}, test_user)
        expect_error_response(:forbidden)
      end
    end

    context 'with empty results' do
      before do
        TemplateCategory.destroy_all
      end

      it 'returns empty result when no categories exist' do
        index_template_categories({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 0
      end
    end
  end
end
