# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Memberships#index', type: :request do
  let!(:admin) { create(:user) }
  let!(:member) { create(:user) }
  let!(:member2) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:organization2) { create(:organization) }
  let!(:membership_admin) { create(:membership, :admin, user: admin, organization:) }
  let!(:membership_member) { create(:membership, user: member, organization:) }
  let!(:membership_member2) { create(:membership, user: member2, organization: organization2) }

  def index_memberships(params, user)
    get '/v1/memberships', params, as_user(user)
  end

  describe 'GET list memberships' do
    it 'return list memberships for admin' do
      index_memberships({}, admin)
      expect_response(:ok)

      expect(response_data.size).to eq 2
    end

    it 'return list filtered by organization_id for admin' do
      index_memberships({ organization_id: organization2.id }, admin)
      expect_response(:ok)

      expect(response_data.size).to eq 1
    end
  end
end
