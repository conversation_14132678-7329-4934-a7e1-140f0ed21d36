# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'PUT /v1/memberships/:id', type: :request do
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:owner) { create(:user) }
  let!(:admin) { create(:user) }
  let!(:team_admin) { create(:user) }
  let!(:partner_admin) { create(:user) }
  let!(:regular_member) { create(:user) }
  let!(:target_member) { create(:user) }
  let!(:other_org_admin) { create(:user) }

  let!(:owner_membership) { create(:membership, :owner, user: owner, organization: organization) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization: organization) }
  let!(:team_admin_membership) { create(:membership, :team_admin, user: team_admin, organization: organization) }
  let!(:partner_admin_membership) do
    create(:membership, user: partner_admin, organization: organization, role: Membership.role_mappings['partner_admin'])
  end
  let!(:member_membership) { create(:membership, user: regular_member, organization: organization) }
  let!(:target_membership) { create(:membership, user: target_member, organization: organization) }
  let!(:other_org_membership) { create(:membership, :admin, user: other_org_admin, organization: other_organization) }

  let!(:organization_team) { create(:organization_team, organization: organization) }
  let!(:organization_team_2) { create(:organization_team, organization: organization) }

  def update_membership(membership_id, params, which_user = admin)
    put "/v1/memberships/#{membership_id}", params, as_user(which_user)
  end

  describe 'PUT /v1/memberships/:id' do
    context 'when admin updates member role' do
      let(:update_params) do
        {
          role: 'team_admin'
        }
      end

      it 'successfully updates member role to team_admin' do
        update_membership(target_membership.id, update_params, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.role).to eq(Membership.role_mappings['team_admin'])
      end

      it 'returns updated membership data' do
        update_membership(target_membership.id, update_params, admin)
        
        expect_response(:ok)
        expect(response_data['role']).to eq(Membership.role_mappings['team_admin'])
        expect(response_data['user_id']).to eq(target_member.id)
      end
    end

    context 'when updating organization team assignments' do
      let(:team_params) do
        {
          organization_team_ids: [organization_team.id, organization_team_2.id]
        }
      end

      it 'successfully assigns member to organization teams' do
        update_membership(target_membership.id, team_params, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.organization_team_ids).to contain_exactly(organization_team.id, organization_team_2.id)
      end

      it 'handles empty team assignment' do
        update_membership(target_membership.id, { organization_team_ids: [] }, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.organization_team_ids).to eq([])
      end

      it 'removes duplicate team IDs' do
        duplicate_params = { organization_team_ids: [organization_team.id, organization_team.id] }
        
        update_membership(target_membership.id, duplicate_params, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.organization_team_ids).to eq([organization_team.id])
      end
    end

    context 'when updating partner admin managed organizations' do
      let!(:managed_org_1) { create(:organization) }
      let!(:managed_org_2) { create(:organization) }
      
      let(:partner_admin_params) do
        {
          role: 'partner_admin',
          primary_workspace: managed_org_1.id,
          managed_workspaces: [managed_org_2.id]
        }
      end

      it 'successfully updates partner admin with managed organizations' do
        update_membership(target_membership.id, partner_admin_params, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.role).to eq(Membership.role_mappings['partner_admin'])
        expect(target_membership.managed_organization_ids).to contain_exactly(managed_org_1.id, managed_org_2.id)
      end

      it 'handles primary workspace only' do
        primary_only_params = {
          role: 'partner_admin',
          primary_workspace: managed_org_1.id
        }
        
        update_membership(target_membership.id, primary_only_params, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.managed_organization_ids).to eq([managed_org_1.id])
      end
    end

    context 'when owner updates admin role' do
      it 'allows owner to update admin role' do
        update_membership(admin_membership.id, { role: 'team_admin' }, owner)
        
        expect_response(:ok)
        
        admin_membership.reload
        expect(admin_membership.role).to eq(Membership.role_mappings['team_admin'])
      end
    end

    context 'when team admin updates member role' do
      it 'allows team admin to update member role' do
        update_membership(target_membership.id, { role: 'member' }, team_admin)
        
        expect_response(:ok)
      end

      it 'prevents team admin from updating admin role' do
        update_membership(admin_membership.id, { role: 'member' }, team_admin)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when regular member tries to update' do
      it 'denies access to regular members' do
        update_membership(target_membership.id, { role: 'team_admin' }, regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when trying to update membership from different organization' do
      it 'denies access to memberships from other organizations' do
        update_membership(other_org_membership.id, { role: 'member' }, admin)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when updating non-existent membership' do
      it 'returns not found error' do
        update_membership(99999, { role: 'member' }, admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'with invalid role values' do
      it 'returns validation error for invalid role' do
        update_membership(target_membership.id, { role: 'invalid_role' }, admin)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation error for empty role' do
        update_membership(target_membership.id, { role: '' }, admin)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'with invalid organization team IDs' do
      it 'returns error for non-existent team ID' do
        update_membership(target_membership.id, { organization_team_ids: [99999] }, admin)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error for team from different organization' do
        other_org_team = create(:organization_team, organization: other_organization)
        
        update_membership(target_membership.id, { organization_team_ids: [other_org_team.id] }, admin)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when platform admin updates across organizations' do
      let!(:platform_admin) { create(:user) }
      let!(:platform_admin_membership) do
        create(:membership, user: platform_admin, organization: organization, role: Membership.role_mappings['admin'])
      end

      it 'allows platform admin to update memberships in any organization' do
        update_membership(other_org_membership.id, { role: 'member' }, platform_admin)
        
        expect_response(:ok)
        
        other_org_membership.reload
        expect(other_org_membership.role).to eq(Membership.role_mappings['member'])
      end
    end

    context 'when updating owner_platform role' do
      let!(:owner_platform) { create(:user) }
      let!(:owner_platform_membership) do
        create(:membership, user: owner_platform, organization: organization, role: Membership.role_mappings['owner_platform'])
      end

      it 'demotes owner_platform to super_admin_platform when updated to owner_platform' do
        update_membership(owner_platform_membership.id, { role: 'owner_platform' }, owner_platform)
        
        expect_response(:ok)
        
        # Find the membership that was demoted
        demoted_membership = Membership.find_by(user: owner_platform, organization: organization)
        expect(demoted_membership.role).to eq(Membership.role_mappings['super_admin_platform'])
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        put "/v1/memberships/#{target_membership.id}", { role: 'member' }
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with complex role hierarchy scenarios' do
      it 'prevents lower role from promoting to higher role' do
        # Team admin trying to promote member to admin
        update_membership(target_membership.id, { role: 'admin' }, team_admin)
        
        expect_error_response(:unauthorized)
      end

      it 'allows same level role changes' do
        # Admin changing member to team_admin (both lower than admin)
        update_membership(target_membership.id, { role: 'team_admin' }, admin)
        
        expect_response(:ok)
      end
    end

    context 'when updating multiple fields simultaneously' do
      let(:complex_params) do
        {
          role: 'team_admin',
          organization_team_ids: [organization_team.id]
        }
      end

      it 'successfully updates multiple fields at once' do
        update_membership(target_membership.id, complex_params, admin)
        
        expect_response(:ok)
        
        target_membership.reload
        expect(target_membership.role).to eq(Membership.role_mappings['team_admin'])
        expect(target_membership.organization_team_ids).to eq([organization_team.id])
      end
    end
  end
end
