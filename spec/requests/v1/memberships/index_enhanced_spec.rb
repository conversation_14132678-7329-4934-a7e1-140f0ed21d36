# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/memberships - Enhanced Coverage', type: :request do
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:platform_admin) { create(:user) }
  let!(:admin) { create(:user) }
  let!(:team_admin) { create(:user) }
  let!(:partner_admin) { create(:user) }
  let!(:regular_member) { create(:user) }
  let!(:member_2) { create(:user) }
  let!(:other_org_member) { create(:user) }

  let!(:platform_admin_membership) do
    create(:membership, user: platform_admin, organization: organization, role: Membership.role_mappings['admin'])
  end
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization: organization) }
  let!(:team_admin_membership) { create(:membership, :team_admin, user: team_admin, organization: organization) }
  let!(:partner_admin_membership) do
    create(:membership, user: partner_admin, organization: organization, role: Membership.role_mappings['partner_admin'])
  end
  let!(:member_membership) { create(:membership, user: regular_member, organization: organization) }
  let!(:member_2_membership) { create(:membership, user: member_2, organization: organization) }
  let!(:other_org_membership) { create(:membership, user: other_org_member, organization: other_organization) }

  let!(:organization_team) { create(:organization_team, organization: organization) }
  let!(:organization_team_2) { create(:organization_team, organization: organization) }

  def index_memberships(params = {}, which_user = admin)
    get '/v1/memberships', params, as_user(which_user)
  end

  describe 'GET /v1/memberships - Filtering and Authorization' do
    context 'when platform admin queries across organizations' do
      it 'allows platform admin to filter by specific organization' do
        index_memberships({ organization_id: other_organization.id }, platform_admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['organization_id']).to eq(other_organization.id)
      end

      it 'returns all organizations when no filter specified' do
        index_memberships({}, platform_admin)
        
        expect_response(:ok)
        # Should include memberships from both organizations
        org_ids = response_data.map { |m| m['organization_id'] }.uniq
        expect(org_ids).to contain_exactly(organization.id, other_organization.id)
      end
    end

    context 'when regular admin queries memberships' do
      it 'restricts results to own organization only' do
        index_memberships({}, admin)
        
        expect_response(:ok)
        org_ids = response_data.map { |m| m['organization_id'] }.uniq
        expect(org_ids).to eq([organization.id])
        expect(response_data.size).to eq(6) # All members in organization
      end

      it 'ignores organization_id filter for non-platform admins' do
        index_memberships({ organization_id: other_organization.id }, admin)
        
        expect_response(:ok)
        # Should still return own organization memberships
        org_ids = response_data.map { |m| m['organization_id'] }.uniq
        expect(org_ids).to eq([organization.id])
      end
    end

    context 'when filtering by role' do
      it 'filters by single role as string' do
        index_memberships({ role: '["admin"]' }, admin)
        
        expect_response(:ok)
        roles = response_data.map { |m| m['role'] }.uniq
        expect(roles).to eq([Membership.role_mappings['admin']])
      end

      it 'filters by multiple roles as JSON array' do
        index_memberships({ role: '["admin", "team_admin"]' }, admin)
        
        expect_response(:ok)
        roles = response_data.map { |m| m['role'] }.uniq.sort
        expected_roles = [Membership.role_mappings['admin'], Membership.role_mappings['team_admin']].sort
        expect(roles).to eq(expected_roles)
      end

      it 'returns error for invalid JSON role format' do
        index_memberships({ role: 'invalid_json' }, admin)
        
        expect_error_response(:unprocessable_entity)
        error_message = response_body[:errors][0][:message]
        expect(error_message).to include('Role(s) Format Invalid')
      end

      it 'handles empty role array' do
        index_memberships({ role: '[]' }, admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(0)
      end
    end

    context 'when filtering by organization team' do
      before do
        member_membership.update!(organization_team_ids: [organization_team.id])
        member_2_membership.update!(organization_team_ids: [organization_team_2.id])
      end

      it 'filters by organization team ID' do
        index_memberships({ organization_team_id: organization_team.id }, admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['user_id']).to eq(regular_member.id)
      end

      it 'returns error for invalid team ID' do
        index_memberships({ organization_team_id: 99999 }, admin)
        
        expect_error_response(:unprocessable_entity)
        error_message = response_body[:errors][0][:message]
        expect(error_message).to include('Team Invalid')
      end

      it 'allows platform admin to query teams across organizations' do
        other_org_team = create(:organization_team, organization: other_organization)
        other_org_membership.update!(organization_team_ids: [other_org_team.id])
        
        index_memberships({ organization_team_id: other_org_team.id }, platform_admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
      end

      it 'prevents regular admin from accessing teams in other organizations' do
        other_org_team = create(:organization_team, organization: other_organization)
        
        index_memberships({ organization_team_id: other_org_team.id }, admin)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when filtering by name' do
      before do
        regular_member.update!(display_name: 'John Doe')
        member_2.update!(display_name: 'Jane Smith')
      end

      it 'filters memberships by user display name' do
        index_memberships({ name: 'John' }, admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['user_id']).to eq(regular_member.id)
      end

      it 'returns empty results for non-matching name' do
        index_memberships({ name: 'NonExistent' }, admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(0)
      end
    end

    context 'when excluding platform admins' do
      it 'excludes platform admin roles for regular admin' do
        index_memberships({}, admin)
        
        expect_response(:ok)
        roles = response_data.map { |m| m['role'] }
        platform_roles = roles.select { |r| r < 0 } # Negative values are platform roles
        expect(platform_roles).to be_empty
      end

      it 'includes platform admin roles for platform admin' do
        index_memberships({}, platform_admin)
        
        expect_response(:ok)
        roles = response_data.map { |m| m['role'] }
        platform_roles = roles.select { |r| r < 0 }
        expect(platform_roles).not_to be_empty
      end

      it 'includes platform admin roles for partner admin' do
        index_memberships({}, partner_admin)
        
        expect_response(:ok)
        roles = response_data.map { |m| m['role'] }
        platform_roles = roles.select { |r| r < 0 }
        expect(platform_roles).not_to be_empty
      end
    end

    context 'with pagination parameters' do
      it 'handles page parameter' do
        index_memberships({ page: 1, per_page: 2 }, admin)
        
        expect_response(:ok)
        expect(response_data.size).to be <= 2
      end

      it 'handles per_page parameter' do
        index_memberships({ per_page: 3 }, admin)
        
        expect_response(:ok)
        expect(response_data.size).to be <= 3
      end
    end

    context 'when user has no organization membership' do
      let!(:no_membership_user) { create(:user) }

      it 'returns error for user without organization' do
        index_memberships({}, no_membership_user)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get '/v1/memberships', {}
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with complex filtering combinations' do
      before do
        member_membership.update!(organization_team_ids: [organization_team.id])
        regular_member.update!(display_name: 'Test Member')
      end

      it 'combines multiple filters correctly' do
        params = {
          role: '["member"]',
          organization_team_id: organization_team.id,
          name: 'Test'
        }
        
        index_memberships(params, admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['user_id']).to eq(regular_member.id)
      end
    end

    context 'with discarded memberships' do
      before do
        member_2_membership.discard!
      end

      it 'excludes discarded memberships from results' do
        index_memberships({}, admin)
        
        expect_response(:ok)
        user_ids = response_data.map { |m| m['user_id'] }
        expect(user_ids).not_to include(member_2.id)
      end
    end

    context 'response format validation' do
      it 'includes users data in response' do
        index_memberships({}, admin)
        
        expect_response(:ok)
        expect(response.parsed_body).to have_key('users')
        expect(response.parsed_body['users']).to be_an(Array)
      end

      it 'includes organization_teams data in response' do
        index_memberships({}, admin)
        
        expect_response(:ok)
        expect(response.parsed_body).to have_key('organization_teams')
        expect(response.parsed_body['organization_teams']).to be_an(Array)
      end

      it 'returns membership data with correct structure' do
        index_memberships({}, admin)
        
        expect_response(:ok)
        membership_data = response_data.first
        expect(membership_data).to have_key('id')
        expect(membership_data).to have_key('user_id')
        expect(membership_data).to have_key('organization_id')
        expect(membership_data).to have_key('role')
      end
    end
  end
end
