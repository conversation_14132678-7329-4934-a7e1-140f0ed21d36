# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'POST /v1/agent_workflow_nodes', type: :request do
  let!(:admin) { create(:user) }
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization:) }
  let!(:user_membership) { create(:membership, :member, user:, organization:) }
  let!(:agent_workflow) { create(:agent_workflow, organization:) }
  let!(:model_bank) { create(:model_bank) }
  let!(:model_template) { create(:model_template) }
  let!(:knowledge_base_file) { create(:knowledge_base_file, organization:) }

  let(:params) do
    {
      agent_workflow_id: agent_workflow.id,
      model_bank_id: model_bank.id,
      workflow_type: 'agent',
      model_template_id: model_template.id,
      reference_output: 'Sample reference output',
      rules: 'Sample rules'
    }
  end

  def create_node(params, which_user = user)
    post '/v1/agent_workflow_nodes', params, as_user(which_user)
  end

  describe 'Create Agent Workflow Node' do
    context 'when request is valid' do
      it 'returns created status and persists the node' do
        create_node(params, user)
        expect_response(
          :created,
          data: {
            id: Integer,
            name: nil,
            description: nil,
            workflow_type: 'agent',
            reference_output: 'Sample reference output',
            rules: 'Sample rules',
            order_level: 0,
            model_template: Hash,
            model: Hash,
            knowledge_base_file: Hash
          }
        )

        new_node = AgentWorkflowNode.find(response_data[:id])
        expect(new_node.agent_workflow_id).to eq params[:agent_workflow_id]
        expect(new_node.model_bank_id).to eq params[:model_bank_id]
        expect(new_node.model_template_id).to eq params[:model_template_id]
        expect(new_node.workflow_type).to eq params[:workflow_type]
        expect(new_node.reference_output).to eq params[:reference_output]
        expect(new_node.rules).to eq params[:rules]
      end

      it 'creates a node with knowledge_base_file' do
        params[:knowledge_base_file_id] = knowledge_base_file.id
        create_node(params, user)
        expect_response(:created)

        new_node = AgentWorkflowNode.find(response_data[:id])
        expect(new_node.knowledge_base_file_id).to eq knowledge_base_file.id
      end

      it 'creates a merger node without model_template_id' do
        merger_params = params.except(:model_template_id).merge(workflow_type: 'merger')
        create_node(merger_params, user)
        expect_response(:created)

        new_node = AgentWorkflowNode.find(response_data[:id])
        expect(new_node.workflow_type).to eq 'merger'
        expect(new_node.model_template_id).to be_nil
      end
    end

    context 'when request is invalid' do
      it 'returns error when agent_workflow_id is missing' do
        create_node(params.except(:agent_workflow_id), user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error when model_bank_id is missing' do
        create_node(params.except(:model_bank_id), user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error when workflow_type is missing' do
        create_node(params.except(:workflow_type), user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error when workflow_type is invalid' do
        invalid_params = params.merge(workflow_type: 'invalid_type')
        create_node(invalid_params, user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error when agent_workflow is not found' do
        invalid_params = params.merge(agent_workflow_id: 999)
        create_node(invalid_params, user)
        expect_error_response(:not_found)
      end

      it 'returns error when model_bank is not found' do
        invalid_params = params.merge(model_bank_id: 999)
        create_node(invalid_params, user)
        expect_error_response(:not_found)
      end

      it 'returns error when model_template is not found' do
        invalid_params = params.merge(model_template_id: 999)
        create_node(invalid_params, user)
        expect_error_response(:not_found)
      end

      it 'returns error when knowledge_base_file is not found' do
        invalid_params = params.merge(knowledge_base_file_id: 999)
        create_node(invalid_params, user)
        expect_error_response(:not_found)
      end
    end
  end
end
