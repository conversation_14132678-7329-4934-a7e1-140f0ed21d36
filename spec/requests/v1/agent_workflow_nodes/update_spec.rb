# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'PUT /v1/agent_workflow_nodes/:id', type: :request do
  let!(:admin) { create(:user) }
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization:) }
  let!(:user_membership) { create(:membership, :member, user:, organization:) }
  let!(:agent_workflow) { create(:agent_workflow, organization:) }
  let!(:model_bank) { create(:model_bank) }
  let!(:new_model_bank) { create(:model_bank) }
  let!(:model_template) { create(:model_template) }
  let!(:new_model_template) { create(:model_template) }
  let!(:knowledge_base_file) { create(:knowledge_base_file, organization:) }
  let!(:new_knowledge_base_file) { create(:knowledge_base_file, organization:) }

  let!(:agent_node) do
    create(
      :agent_workflow_node,
      agent_workflow: agent_workflow,
      model_bank: model_bank,
      model_template: model_template,
      workflow_type: 'agent',
      reference_output: 'Original reference output',
      rules: 'Original rules'
    )
  end

  let!(:merger_node) do
    create(
      :agent_workflow_node,
      agent_workflow: agent_workflow,
      model_bank: model_bank,
      workflow_type: 'merger',
      reference_output: 'Original reference output',
      rules: 'Original rules'
    )
  end

  let(:update_params) do
    {
      model_bank_id: new_model_bank.id,
      reference_output: 'Updated reference output',
      rules: 'Updated rules'
    }
  end

  def update_node(id, params, which_user = user)
    put "/v1/agent_workflow_nodes/#{id}", params, as_user(which_user)
  end

  describe 'Update Agent Workflow Node' do
    context 'when request is valid' do
      it 'updates an agent node and returns success' do
        update_node(agent_node.id, update_params, user)
        expect_response(:ok)

        agent_node.reload
        expect(agent_node.model_bank_id).to eq new_model_bank.id
        expect(agent_node.reference_output).to eq 'Updated reference output'
        expect(agent_node.rules).to eq 'Updated rules'
      end

      it 'updates a merger node and returns success' do
        update_node(merger_node.id, update_params, user)
        expect_response(:ok)

        merger_node.reload
        expect(merger_node.model_bank_id).to eq new_model_bank.id
        expect(merger_node.reference_output).to eq 'Updated reference output'
        expect(merger_node.rules).to eq 'Updated rules'
      end

      it 'updates model_template for agent node' do
        params = update_params.merge(model_template_id: new_model_template.id)
        update_node(agent_node.id, params, user)
        expect_response(:ok)

        agent_node.reload
        expect(agent_node.model_template_id).to eq new_model_template.id
      end

      it 'updates knowledge_base_file' do
        params = update_params.merge(knowledge_base_file_id: new_knowledge_base_file.id)
        update_node(agent_node.id, params, user)
        expect_response(:ok)

        agent_node.reload
        expect(agent_node.knowledge_base_file_id).to eq new_knowledge_base_file.id
      end

      it 'changes workflow_type from agent to merger' do
        params = update_params.merge(workflow_type: 'merger', model_template_id: nil)
        update_node(agent_node.id, params, user)
        expect_response(:ok)

        agent_node.reload
        expect(agent_node.workflow_type).to eq 'merger'
        expect(agent_node.model_template_id).to be_nil
      end

      it 'changes workflow_type from merger to agent' do
        params = update_params.merge(workflow_type: 'agent', model_template_id: model_template.id)
        update_node(merger_node.id, params, user)
        expect_response(:ok)

        merger_node.reload
        expect(merger_node.workflow_type).to eq 'agent'
        expect(merger_node.model_template_id).to eq model_template.id
      end
    end

    context 'when request is invalid' do
      it 'returns error when node is not found' do
        update_node(999, update_params, user)
        expect_error_response(:not_found)
      end

      it 'returns error when model_bank is not found' do
        invalid_params = update_params.merge(model_bank_id: 999)
        update_node(agent_node.id, invalid_params, user)
        expect_error_response(:not_found)
      end

      it 'returns error when model_template is not found' do
        invalid_params = update_params.merge(model_template_id: 999)
        update_node(agent_node.id, invalid_params, user)
        expect_error_response(:not_found)
      end

      it 'returns error when knowledge_base_file is not found' do
        invalid_params = update_params.merge(knowledge_base_file_id: 999)
        update_node(agent_node.id, invalid_params, user)
        expect_error_response(:not_found)
      end

      xit 'returns error when trying to set model_template_id for merger node' do
        invalid_params = update_params.merge(model_template_id: model_template.id)
        update_node(merger_node.id, invalid_params, user)
        expect_error_response(:unprocessable_entity, 'Merger node must not have model template')
      end

      xit 'returns error when trying to remove model_template_id from agent node' do
        invalid_params = update_params.merge(model_template_id: nil)
        update_node(agent_node.id, invalid_params, user)
        expect_error_response(:unprocessable_entity, 'Agent node must have model template')
      end

      it 'returns error when workflow_type is invalid' do
        invalid_params = update_params.merge(workflow_type: 'invalid_type')
        update_node(agent_node.id, invalid_params, user)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'with authorization' do
      it 'allows admin to update node' do
        update_node(agent_node.id, update_params, admin)
        expect_response(:ok)
      end

      it 'allows member to update node' do
        update_node(agent_node.id, update_params, user)
        expect_response(:ok)
      end
    end
  end
end
