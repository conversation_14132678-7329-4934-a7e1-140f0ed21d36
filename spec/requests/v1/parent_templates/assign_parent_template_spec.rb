# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::ParentTemplates', type: :request do
  let!(:test_user) { create(:user, display_name: 'Test') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:membership_test_user) { create(:membership, user: test_user, organization: organization) }
  let!(:workspace_membership) { create(:workspaces_membership, membership: membership_test_user, workspace: workspace) }

  # Platform admin users
  let!(:platform_admin) { create(:user, display_name: 'Platform Admin') }
  let!(:membership_platform_admin) { create(:membership, :admin, user: platform_admin, organization: organization) }

  # Partner admin user
  let!(:partner_admin) { create(:user, display_name: 'Partner Admin') }
  let!(:organization_created_by_partner) { create(:organization, name: 'Partner Org', created_by_id: partner_admin.id) }
  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: organization_created_by_partner)
  end

  let!(:model) { create(:model, name: 'ChatGPT gpt-4o', organization: organization) }
  let!(:parent_template) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Parent Template',
           in_bank: true, 
           verified: true)
  end
  
  let!(:template_without_parent) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Template Without Parent',
           in_bank: false)
  end
  
  let!(:partner_template) do
    create(:model_template, 
           organization: organization_created_by_partner, 
           user: partner_admin, 
           name: 'Partner Template',
           in_bank: true, 
           verified: true)
  end

  def assign_parent_template(template_id, parent_id, user)
    post "/v1/parent_templates/#{template_id}/assign", 
        { parent_id: parent_id }, 
        as_user(user)
  end

  describe 'POST assign_parent_template' do
    it 'assigns parent template successfully' do
      assign_parent_template(template_without_parent.id, parent_template.id, platform_admin)
      expect_response(:ok)

      # Verify the parent was assigned
      expect(response_data['parent_id']).to eq(parent_template.id)
      expect(response_data['id']).to eq(template_without_parent.id)
    end

    it 'fails without proper authorization' do
      assign_parent_template(template_without_parent.id, parent_template.id, test_user)
      expect_response(:forbidden)
    end

    it 'partner admin can assign parent templates in their created organization' do
      partner_template_without_parent = create(
        :model_template, 
        organization: organization_created_by_partner, 
        user: partner_admin, 
        name: 'Partner Template Without Parent'
      )
      
      assign_parent_template(partner_template_without_parent.id, partner_template.id, partner_admin)
      expect_response(:ok)
      
      expect(response_data['parent_id']).to eq(partner_template.id)
    end

    it 'partner admin cannot assign parent templates in other organizations' do
      assign_parent_template(template_without_parent.id, parent_template.id, partner_admin)
      expect_response(:forbidden)
    end
  end
end