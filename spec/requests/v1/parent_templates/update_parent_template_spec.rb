# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::ModelTemplateBanks', type: :request do
  let!(:test_user) { create(:user, display_name: 'Test', email: '<EMAIL>') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:membership_test_user) { create(:membership, user: test_user, organization: organization) }
  let!(:workspace_membership) { create(:workspaces_membership, membership: membership_test_user, workspace: workspace) }

  # Platform admin users
  let!(:platform_admin) { create(:user, display_name: 'Platform Admin', email: '<EMAIL>') }
  let!(:membership_platform_admin) { create(:membership, :admin, user: platform_admin, organization: organization) }

  # Partner admin user
  let!(:partner_admin) { create(:user, display_name: 'Partner Admin', email: '<EMAIL>') }
  let!(:organization_created_by_partner) { create(:organization, name: 'Partner Org', created_by_id: partner_admin.id) }
  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: organization_created_by_partner)
  end

  let!(:model) { create(:model, name: 'ChatGPT gpt-4o', organization: organization) }
  let!(:parent_template) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Parent Template',
           description: 'Original description',
           instruction: 'Original instruction',
           in_bank: true, 
           verified: true)
  end
  
  let!(:child_template) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Child Template',
           parent_id: parent_template.id,
           in_bank: false)
  end
  
  let!(:partner_template) do
    create(:model_template, 
           organization: organization_created_by_partner, 
           user: partner_admin, 
           name: 'Partner Template',
           description: 'Partner description',
           in_bank: true, 
           verified: true)
  end

  def update_parent_template(template_id, params, user)
    put "/v1/parent_templates/#{template_id}", 
        params, 
        as_user(user)
  end

  describe 'PUT update_parent_template' do
    it 'updates parent template successfully' do
      update_params = {
        name: 'Updated Parent Template',
        description: 'Updated description',
        instruction: 'Updated instruction'
      }
      
      update_parent_template(parent_template.id, update_params, platform_admin)
      expect_response(:ok)

      # Verify the template was updated
      expect(response_data['name']).to eq('Updated Parent Template')
      expect(response_data['description']).to eq('Updated description')
      expect(response_data['instruction']).to eq('Updated instruction')
      
      # Verify the ID remains the same
      expect(response_data['id']).to eq(parent_template.id)
    end

    it 'fails without proper authorization' do
      update_params = { name: 'Unauthorized Update' }
      update_parent_template(parent_template.id, update_params, test_user)
      expect_response(:forbidden)
    end

    it 'partner admin can update templates from their created organization' do
      update_params = { name: 'Updated Partner Template' }
      update_parent_template(partner_template.id, update_params, partner_admin)
      expect_response(:ok)
      
      expect(response_data['name']).to eq('Updated Partner Template')
    end

    it 'partner admin cannot update templates from other organizations' do
      update_params = { name: 'Unauthorized Partner Update' }
      update_parent_template(parent_template.id, update_params, partner_admin)
      expect_response(:forbidden)
    end
  end
end