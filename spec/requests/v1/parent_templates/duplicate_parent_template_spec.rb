# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ModelTemplateBanksController, type: :request do
  let!(:test_user) { create(:user, display_name: 'Test', email: '<EMAIL>') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:membership_test_user) { create(:membership, user: test_user, organization: organization) }
  let!(:workspace_membership) { create(:workspaces_membership, membership: membership_test_user, workspace: workspace) }

  # Platform admin users
  let!(:platform_admin) { create(:user, display_name: 'Platform Admin', email: '<EMAIL>') }
  let!(:membership_platform_admin) { create(:membership, :admin, user: platform_admin, organization: organization) }

  # Partner admin user
  let!(:partner_admin) { create(:user, display_name: 'Partner Admin', email: '<EMAIL>') }
  let!(:organization_created_by_partner) { create(:organization, name: 'Partner Org', created_by_id: partner_admin.id) }
  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: organization_created_by_partner)
  end

  let!(:model) { create(:model, name: 'ChatGPT gpt-4o', organization: organization) }
  let!(:parent_template) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Parent Template',
           description: 'Original description',
           instruction: 'Original instruction',
           in_bank: true, 
           verified: true)
  end
  
  let!(:partner_template) do
    create(:model_template, 
           organization: organization_created_by_partner, 
           user: partner_admin, 
           name: 'Partner Template',
           description: 'Partner description',
           in_bank: true, 
           verified: true)
  end

  def duplicate_parent_template(template_id, user)
    post "/v1/parent_templates/#{template_id}/duplicate", {}, as_user(user)
  end

  describe 'POST duplicate_parent_template' do
    it 'duplicates parent template successfully' do
      duplicate_parent_template(parent_template.id, platform_admin)
      expect_response(:ok)

      # Verify the duplicated template has the expected properties
      expect(response_data['name']).to eq("#{parent_template.name} - Copy")
      expect(response_data['description']).to eq(parent_template.description)
      expect(response_data['instruction']).to eq(parent_template.instruction)
      expect(response_data['id']).not_to eq(parent_template.id)
      expect(response_data['in_bank']).to be false
    end

    it 'fails without proper authorization' do
      duplicate_parent_template(parent_template.id, test_user)
      expect_response(:forbidden)
    end

    it 'partner admin can duplicate templates from their created organization' do
      duplicate_parent_template(partner_template.id, partner_admin)
      expect_response(:ok)
      
      expect(response_data['name']).to eq("#{partner_template.name} - Copy")
      expect(response_data['id']).not_to eq(partner_template.id)
    end

    it 'partner admin cannot duplicate templates from other organizations' do
      duplicate_parent_template(parent_template.id, partner_admin)
      expect_response(:forbidden)
    end
  end
end