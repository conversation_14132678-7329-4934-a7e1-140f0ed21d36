# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ModelTemplateBanksController, type: :request do
  let!(:test_user) { create(:user, display_name: 'Test', email: '<EMAIL>') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:membership_test_user) { create(:membership, user: test_user, organization: organization) }
  let!(:workspace_membership) { create(:workspaces_membership, membership: membership_test_user, workspace: workspace) }

  # Platform admin users
  let!(:platform_admin) { create(:user, display_name: 'Platform Admin', email: '<EMAIL>') }
  let!(:membership_platform_admin) { create(:membership, :admin, user: platform_admin, organization: organization) }

  # Partner admin user
  let!(:partner_admin) { create(:user, display_name: 'Partner Admin', email: '<EMAIL>') }
  let!(:organization_created_by_partner) { create(:organization, name: 'Partner Org', created_by_id: partner_admin.id) }
  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: organization_created_by_partner)
  end

  let!(:model) { create(:model, name: 'ChatGPT gpt-4o', organization: organization) }
  let!(:parent_template) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Parent Template',
           in_bank: true, 
           verified: true)
  end
  
  let!(:child_template) do
    create(:model_template, 
           organization: organization, 
           user: test_user, 
           name: 'Child Template',
           parent_id: parent_template.id,
           in_bank: false)
  end
  
  let!(:partner_template) do
    create(:model_template, 
           organization: organization_created_by_partner, 
           user: partner_admin, 
           name: 'Partner Template',
           in_bank: true, 
           verified: true)
  end

  def destroy_parent_template(template_id, user)
    delete "/v1/parent_templates/#{template_id}", {}, as_user(user)
  end

  describe 'DELETE destroy_parent_template' do
    it 'destroys parent template successfully' do
      template_id = parent_template.id
      destroy_parent_template(template_id, platform_admin)
      expect_response(:ok)

      # Verify the template was deleted
      expect(ModelTemplate.find_by(id: template_id)).to be_nil
    end

    it 'fails without proper authorization' do
      destroy_parent_template(parent_template.id, test_user)
      expect_response(:forbidden)
    end

    it 'partner admin can destroy templates from their created organization' do
      template_id = partner_template.id
      destroy_parent_template(template_id, partner_admin)
      expect_response(:ok)
      
      # Verify the template was deleted
      expect(ModelTemplate.find_by(id: template_id)).to be_nil
    end

    it 'partner admin cannot destroy templates from other organizations' do
      destroy_parent_template(parent_template.id, partner_admin)
      expect_response(:forbidden)
    end
  end
end