# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Auth#authenticate', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test', code: 'test')
  end

  let!(:organization2) do
    Organization.create!(name: 'Test2', code: 'org-2')
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization.id,
      role: 3
    )
  end

  let!(:membership_partner_admin) do
    Membership.create!(
      user_id: partner_admin.id,
      organization_id: organization.id,
      role: -1
    )
  end

  let!(:organization_plan_thresholds) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      monthly_credits_refresh: 100,
      max_members: 10,
      max_workspaces: 10,
      purchased_credits: 100,
      refresh_date: 1
    )
  end

  def login(params)
    post '/v1/login', params
  end

  describe 'POST /v1/login' do
    context 'when user is partner admin' do
      it 'returns error when login to non owned org' do
        login({ email: partner_admin.email, password: '12345678', code: organization2.code })
        expect_response(:unauthorized)
      end

      it 'return success when login to main org' do
        login({ email: partner_admin.email, password: '12345678', code: organization.code })
        expect_response(:ok)
      end

      it 'return success when login to owned org' do
        organization2.update!(created_by_id: partner_admin.id.to_s)
        login({ email: partner_admin.email, password: '12345678', code: organization2.code })
        expect_response(:ok)
      end

      it 'return success when code using UPPERCASE' do
        organization2.update!(created_by_id: partner_admin.id.to_s)
        login({ email: partner_admin.email, password: '12345678', code: organization2.code.upcase })
        expect_response(:ok)
      end

      it 'return success even when code in db is UPPERCASE' do
        organization2.update!(created_by_id: partner_admin.id.to_s, code: organization2.code.upcase)
        login({ email: partner_admin.email, password: '12345678', code: organization2.code.downcase })
        expect_response(:ok)
      end
    end

    it 'returns error when login to discarded org' do
      organization.discard
      login({ email: member.email, password: '12345678', code: organization.code })
      expect_response(:unauthorized)
      organization.undiscard
    end

    it 'return error when organization not found' do
      login({ email: admin.email, password: '12345678', code: 'test-2' })
      expect_response(:unauthorized)
    end

    it 'return error when user not found' do
      login({ email: '<EMAIL>', password: '12345678', code: 'test' })
      expect_response(:unauthorized)
    end

    it 'return error when password is wrong' do
      login({ email: admin.email, password: '123456789', code: 'test' })
      expect_response(:unauthorized)
    end

    it 'return success when login as admin' do
      login({ email: admin.email, password: '12345678', code: 'test' })
      expect_response(:ok)
    end

    it 'return success when login as member' do
      login({ email: member.email, password: '12345678', code: 'test' })
      expect_response(:ok)
    end

    it 'return success with correct data' do
      login({ email: admin.email, password: '12345678', code: 'test' })
      expect_response(:ok)

      expect(response_data['auth_token'].present?).to be_truthy
      expect(response_data['authenticated']).to be_truthy
      expect(response_data['user']['email']).to eq admin.email
      expect(response_data['organization']['code']).to eq 'test'
    end
  end
end
