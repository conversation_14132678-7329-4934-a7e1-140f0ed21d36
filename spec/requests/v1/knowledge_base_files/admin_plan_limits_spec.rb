# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#plan_limits - Admin Access', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:organization2) do
    Organization.create!(name: 'Test Organization 2', code: 'test-org-2')
  end

  let!(:organization3) do
    Organization.create!(name: 'Test Organization 3', code: 'test-org-3', created_by_id: partner_admin.id)
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: -3 # admin
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization.id,
      role: 0 # member
    )
  end

  let!(:membership_partner_admin) do
    Membership.create!(
      user_id: partner_admin.id,
      organization_id: organization.id,
      role: -1 # partner_admin
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 20,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:plan_threshold2) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization2.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 500,
      max_members: 5,
      max_workspaces: 3,
      purchased_credits: 250.0,
      remaining_monthly_credits: 100.0,
      refresh_date: 1
    )
  end

  let!(:plan_threshold3) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization3.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 500,
      max_members: 5,
      max_workspaces: 3,
      purchased_credits: 250.0,
      remaining_monthly_credits: 100.0,
      refresh_date: 1
    )
  end

  # Create some test data to verify usage calculations
  let!(:knowledge_base_file) do
    KnowledgeBaseFile.create!(
      organization_id: organization.id,
      file_url: 'https://example.com/test.pdf',
      name: 'Test File',
      filename: 'test.pdf'
    )
  end

  def get_plan_limits(org_id = nil, user = admin)
    params = org_id ? { organization_id: org_id } : {}
    get '/v1/knowledge_base_files/plan_limits', params, as_user(user)
  end

  describe 'GET plan_limits with admin access' do
    context 'when user is admin' do
      it 'returns plan limits for own organization by default' do
        get_plan_limits(nil, admin)
        expect_response(:ok)

        expect(response_data['max_files']).to eq 20
        expect(response_data['current_files']).to eq 1
        expect(response_data['remaining_files']).to eq 19
      end

      it 'returns plan limits for other organization when organization_id specified' do
        get_plan_limits(organization2.id, admin)
        expect_response(:ok)

        expect(response_data['max_files']).to eq 10
        expect(response_data['current_files']).to eq 0
        expect(response_data['remaining_files']).to eq 10
      end

      it 'returns error when organization does not exist' do
        get_plan_limits(-1, admin)
        expect_response(:not_found)
      end
    end

    context 'when user is partner admin' do
      it 'returns plan limits for own organization by default' do
        get_plan_limits(organization3.id, partner_admin)
        expect_response(:ok)

        expect(response_data['max_files']).to eq 10
        expect(response_data['current_files']).to eq 0
        expect(response_data['remaining_files']).to eq 10
      end

      it 'returns plan limits for organization created by user' do
        organization2.update!(created_by_id: partner_admin.id)
        get_plan_limits(organization2.id, partner_admin)
        expect_response(:ok)

        expect(response_data['max_files']).to eq 10
        expect(response_data['current_files']).to eq 0
        expect(response_data['remaining_files']).to eq 10
      end

      it 'returns error when organization is not created by user' do
        get_plan_limits(organization2.id, partner_admin)
        expect_response(:forbidden)
      end
    end

    context 'when user is regular member' do
      it 'returns plan limits for own organization by default' do
        get_plan_limits(nil, member)
        expect_response(:ok)

        expect(response_data['max_files']).to eq 20
        expect(response_data['current_files']).to eq 1
        expect(response_data['remaining_files']).to eq 19
      end

      it 'returns error when trying to access other organization' do
        get_plan_limits(organization2.id, member)
        expect_response(:forbidden)
      end
    end
  end
end
