# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#multi_organization_access', type: :request do
  let!(:platform_admin) do
    User.create!(
      display_name: 'Platform Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:regular_user) do
    User.create!(
      display_name: 'Regular User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization1) do
    Organization.create!(name: 'Organization 1', code: 'org-1')
  end

  let!(:organization2) do
    Organization.create!(name: 'Organization 2', code: 'org-2')
  end

  let!(:organization3) do
    Organization.create!(name: 'Organization 3', code: 'org-3', created_by_id: partner_admin.id)
  end

  let!(:membership_platform_admin) do
    Membership.create!(
      user_id: platform_admin.id,
      organization_id: organization1.id,
      role: -4 # super_admin_platform
    )
  end

  let!(:membership_partner_admin) do
    Membership.create!(
      user_id: partner_admin.id,
      organization_id: organization1.id,
      role: -1 # partner_admin
    )
  end

  let!(:membership_regular_user) do
    Membership.create!(
      user_id: regular_user.id,
      organization_id: organization1.id,
      role: 0 # member
    )
  end

  let!(:plan_threshold1) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization1.id,
      max_knowledge_base_files: 20,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:plan_threshold2) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization2.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 500,
      max_members: 5,
      max_workspaces: 3,
      purchased_credits: 250.0,
      remaining_monthly_credits: 100.0,
      refresh_date: 1
    )
  end

  let!(:plan_threshold3) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization3.id,
      max_knowledge_base_files: 15,
      monthly_credits_refresh: 750,
      max_members: 8,
      max_workspaces: 4,
      purchased_credits: 375.0,
      remaining_monthly_credits: 150.0,
      refresh_date: 10
    )
  end

  let(:valid_knowledge_base_params) do
    {
      name: 'Test Knowledge Base',
      description: 'Test description',
      filename: 'test.pdf',
      file_url: 'https://example.com/test.pdf',
      content_type: 'application/pdf',
      file_size: 1024
    }
  end

  describe 'Platform Admin Access' do
    context 'when creating knowledge base files' do
      it 'can create files in any organization' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params.merge(organization_id: organization2.id),
             as_user(platform_admin)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(organization2.id)
      end

      it 'can create files in organization created by partner admin' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params.merge(organization_id: organization3.id),
             as_user(platform_admin)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(organization3.id)
      end

      it 'creates files in own organization by default' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params, as_user(platform_admin)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(organization1.id)
      end
    end

    context 'when listing knowledge base files' do
      it 'can list files from specific organization' do
        get '/v1/knowledge_base_files', { organization_id: organization2.id }, as_user(platform_admin)
        expect_response(:ok)

        # Should return empty array since no files exist in organization2
        expect(response_data).to be_empty
      end

      it 'lists files from own organization by default' do
        get '/v1/knowledge_base_files', {}, as_user(platform_admin)
        expect_response(:ok)

        # Should return empty array since no files exist in organization1
        expect(response_data).to be_empty
      end
    end
  end

  describe 'Partner Admin Access' do
    context 'when creating knowledge base files' do
      it 'can create files in own organization' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params, as_user(partner_admin)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(organization1.id)
      end

      it 'can create files in organization created by them' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params.merge(organization_id: organization3.id),
             as_user(partner_admin)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(organization3.id)
      end

      it 'cannot create files in organization not created by them' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params.merge(organization_id: organization2.id),
             as_user(partner_admin)
        expect_error_response(:forbidden)
      end
    end

    context 'when listing knowledge base files' do
      it 'can list files from organization created by them' do
        get '/v1/knowledge_base_files', { organization_id: organization3.id }, as_user(partner_admin)
        expect_response(:ok)

        # Should return empty array since no files exist in organization3
        expect(response_data).to be_empty
      end

      it 'cannot list files from organization not created by them' do
        get '/v1/knowledge_base_files', { organization_id: organization2.id }, as_user(partner_admin)
        expect_error_response(:forbidden)
      end

      it 'lists files from own organization by default' do
        get '/v1/knowledge_base_files', {}, as_user(partner_admin)
        expect_response(:ok)

        # Should return empty array since no files exist in organization1
        expect(response_data).to be_empty
      end
    end
  end

  describe 'Regular User Access' do
    context 'when creating knowledge base files' do
      it 'create files in own organization' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params, as_user(regular_user)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(organization1.id)
      end

      it 'cannot create files in other organizations' do
        post '/v1/knowledge_base_files', valid_knowledge_base_params.merge(organization_id: organization2.id),
             as_user(regular_user)
        expect_error_response(:forbidden)
      end
    end

    context 'when listing knowledge base files' do
      it 'cannot list files from other organizations' do
        get '/v1/knowledge_base_files', { organization_id: organization2.id }, as_user(regular_user)
        expect_error_response(:forbidden)
      end

      it 'lists files from own organization by default' do
        get '/v1/knowledge_base_files', {}, as_user(regular_user)
        expect_response(:ok)

        # Should return empty array since no files exist in organization1
        expect(response_data).to be_empty
      end
    end
  end
end
