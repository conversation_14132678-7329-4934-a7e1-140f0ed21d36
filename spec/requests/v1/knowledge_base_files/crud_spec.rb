# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#crud', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 0 # member
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let(:valid_params) do
    {
      name: 'Test Knowledge Base',
      description: 'Test description',
      filename: 'test.pdf',
      file_url: 'https://example.com/test.pdf',
      content_type: 'application/pdf',
      file_size: 1024
    }
  end

  describe 'POST /v1/knowledge_base_files' do
    context 'with valid parameters' do
      it 'creates a new knowledge base file' do
        post '/v1/knowledge_base_files', valid_params, as_user(user)
        expect_response(:created)

        expect(response_data['name']).to eq('Test Knowledge Base')
        expect(response_data['description']).to eq('Test description')
        expect(response_data['filename']).to eq('test.pdf')
      end
    end

    context 'with invalid parameters' do
      it 'returns validation errors' do
        post '/v1/knowledge_base_files', { description: 'Missing name' }, as_user(user)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when plan limit is exceeded' do
      before do
        # Create files up to the limit
        10.times do |i|
          KnowledgeBaseFile.create!(
            organization_id: organization.id,
            name: "File #{i}",
            filename: "file#{i}.pdf",
            file_url: "https://example.com/file#{i}.pdf"
          )
        end
      end

      it 'returns plan limit error' do
        post '/v1/knowledge_base_files', valid_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end
    end
  end

  describe 'GET /v1/knowledge_base_files/:id' do
    let!(:knowledge_base_file) do
      KnowledgeBaseFile.create!(
        organization_id: organization.id,
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf'
      )
    end

    context 'with valid id' do
      it 'returns the knowledge base file' do
        get "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['id']).to eq(knowledge_base_file.id)
        expect(response_data['name']).to eq('Test File')
      end
    end

    context 'with invalid id' do
      it 'returns not found error' do
        get '/v1/knowledge_base_files/99999', {}, as_user(user)
        expect_error_response(:not_found)
      end
    end
  end

  describe 'PUT /v1/knowledge_base_files/:id' do
    let!(:knowledge_base_file) do
      KnowledgeBaseFile.create!(
        organization_id: organization.id,
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf'
      )
    end

    context 'with valid parameters' do
      it 'updates the knowledge base file' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", {
          name: 'Updated Name',
          description: 'Updated description'
        }, as_user(user)
        expect_response(:ok)

        expect(response_data['name']).to eq('Updated Name')
        expect(response_data['description']).to eq('Updated description')
      end
    end

    context 'with invalid parameters' do
      it 'returns validation errors' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", {
          name: ''
        }, as_user(user)
        expect_error_response(:unprocessable_entity)
      end
    end
  end

  describe 'DELETE /v1/knowledge_base_files/:id' do
    let!(:knowledge_base_file) do
      KnowledgeBaseFile.create!(
        organization_id: organization.id,
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf'
      )
    end

    it 'soft deletes the knowledge base file' do
      delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
      expect_response(:ok)

      knowledge_base_file.reload
      expect(knowledge_base_file.discarded?).to eq(true)
    end
  end

  describe 'GET /v1/knowledge_base_files/plan_limits' do
    context 'when user has access to organization' do
      it 'returns plan limits for the organization' do
        get '/v1/knowledge_base_files/plan_limits', {}, as_user(user)
        expect_response(:ok)

        expect(response_data['max_files']).to eq(10)
        expect(response_data['current_files']).to eq(0)
        expect(response_data['remaining_files']).to eq(10)
      end
    end

    context 'when user does not have access to organization' do
      let!(:other_organization) do
        Organization.create!(name: 'Other Organization', code: 'other-org')
      end

      it 'returns unauthorized error' do
        get '/v1/knowledge_base_files/plan_limits', { organization_id: other_organization.id }, as_user(user)
        expect_error_response(:forbidden)
      end
    end

    context 'when organization does not exist' do
      it 'returns not found error' do
        get '/v1/knowledge_base_files/plan_limits', { organization_id: 99_999 }, as_user(user)
        expect_error_response(:not_found)
      end
    end
  end
end
