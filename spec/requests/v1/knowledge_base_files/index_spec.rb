# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#index', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 0 # member
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:knowledge_base_file) do
    KnowledgeBaseFile.create!(
      organization_id: organization.id,
      file_url: 'https://example.com/test.pdf',
      name: 'Test File',
      filename: 'test.pdf'
    )
  end

  before do
    # Set up Current.user and ensure associations are loaded
    Current.user = user
    user.reload # Ensure associations are loaded
    allow_any_instance_of(V1::ApiController).to receive(:current_user).and_return(user)
  end

  after do
    Current.clear
  end

  describe 'GET /v1/knowledge_base_files' do
    it 'returns list of knowledge base files for the organization' do
      get '/v1/knowledge_base_files', {}, as_user(user)
      expect_response(:ok)

      expect(response_data.size).to eq(1)
      expect(response_data.first['id']).to eq(knowledge_base_file.id)
      expect(response_data.first['name']).to eq('Test File')
    end

    it 'filters by search term' do
      get '/v1/knowledge_base_files', { search: 'Test' }, as_user(user)
      expect_response(:ok)

      expect(response_data.size).to eq(1)
      expect(response_data.first['name']).to eq('Test File')
    end

    it 'filters by active_only' do
      get '/v1/knowledge_base_files', { active_only: true }, as_user(user)
      expect_response(:ok)

      expect(response_data.size).to eq(1)
    end
  end
end
