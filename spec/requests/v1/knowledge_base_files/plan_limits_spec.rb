# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#plan_limits', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 0 # member
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  before do
    # Set up Current.user and ensure associations are loaded
    Current.user = user
    user.reload # Ensure associations are loaded
    allow_any_instance_of(V1::ApiController).to receive(:current_user).and_return(user)
  end

  after do
    Current.clear
  end

  def get_plan_limits
    get '/v1/knowledge_base_files/plan_limits', {}, as_user(user)
  end

  context 'when user is authenticated' do
    it 'returns plan limits for the organization' do
      get_plan_limits

      expect_response(:ok)

      json_response = JSON.parse(response.body)
      expect(json_response['data']['max_files']).to eq(10)
      expect(json_response['data']['current_files']).to eq(0)
      expect(json_response['data']['remaining_files']).to eq(10)
    end

    it 'returns correct counts when files exist' do
      # Create some knowledge base files
      3.times do |i|
        KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: "File #{i}",
          filename: "file#{i}.pdf",
          file_url: "https://example.com/file#{i}.pdf"
        )
      end

      get_plan_limits

      expect_response(:ok)

      json_response = JSON.parse(response.body)
      expect(json_response['data']['max_files']).to eq(10)
      expect(json_response['data']['current_files']).to eq(3)
      expect(json_response['data']['remaining_files']).to eq(7)
    end

    it 'returns 0 remaining files when limit is reached' do
      # Create files up to the limit
      10.times do |i|
        KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: "File #{i}",
          filename: "file#{i}.pdf",
          file_url: "https://example.com/file#{i}.pdf"
        )
      end

      get_plan_limits

      expect_response(:ok)

      json_response = JSON.parse(response.body)
      expect(json_response['data']['max_files']).to eq(10)
      expect(json_response['data']['current_files']).to eq(10)
      expect(json_response['data']['remaining_files']).to eq(0)
    end

    it 'excludes soft-deleted files from count' do
      # Create a file and then soft-delete it
      file = KnowledgeBaseFile.create!(
        organization_id: organization.id,
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf'
      )

      file.discard!

      get_plan_limits

      expect_response(:ok)

      json_response = JSON.parse(response.body)
      expect(json_response['data']['current_files']).to eq(0)
      expect(json_response['data']['remaining_files']).to eq(10)
    end
  end

  context 'when user is not authenticated' do
    it 'returns unauthorized' do
      get '/v1/knowledge_base_files/plan_limits'
      expect_response(:unauthorized)
    end
  end

  context 'when user does not have access to organization' do
    let!(:other_organization) do
      Organization.create!(name: 'Other Organization', code: 'other-org')
    end

    it 'returns unauthorized error' do
      get '/v1/knowledge_base_files/plan_limits', { organization_id: other_organization.id }, as_user(user)
      expect_response(:forbidden)
    end
  end

  context 'when organization does not exist' do
    it 'returns not found error' do
      get '/v1/knowledge_base_files/plan_limits', { organization_id: 99_999 }, as_user(user)
      expect_response(:not_found)
    end
  end
end
