# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles Service Edge Cases', type: :request do
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:owner) { create(:user) }
  let!(:team_admin) { create(:user) }
  let!(:partner_admin) { create(:user) }
  let!(:regular_member) { create(:user) }
  let!(:other_org_user) { create(:user) }

  let!(:owner_membership) { create(:membership, :owner, user: owner, organization: organization) }
  let!(:team_admin_membership) { create(:membership, :team_admin, user: team_admin, organization: organization) }
  let!(:partner_admin_membership) do
    create(:membership, user: partner_admin, organization: organization, role: Membership.role_mappings['partner_admin'])
  end
  let!(:member_membership) { create(:membership, user: regular_member, organization: organization) }
  let!(:other_org_membership) { create(:membership, user: other_org_user, organization: other_organization) }

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 2,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:knowledge_base_file) do
    create(:knowledge_base_file,
           organization: organization,
           name: 'Test File',
           filename: 'test.pdf',
           file_url: 'https://example.com/test.pdf')
  end

  let(:valid_params) do
    {
      name: 'New Knowledge Base File',
      description: 'Test description',
      filename: 'new_file.pdf',
      file_url: 'https://example.com/new_file.pdf',
      content_type: 'application/pdf',
      file_size: 2048
    }
  end

  describe 'POST /v1/knowledge_base_files - Plan Limits' do
    context 'when approaching plan limits' do
      before do
        # Create one more file to reach the limit of 2
        create(:knowledge_base_file, organization: organization)
      end

      it 'prevents creation when plan limit is exceeded' do
        post '/v1/knowledge_base_files', valid_params, as_user(owner)
        
        expect_error_response(:unprocessable_entity)
        error_message = response_body[:errors][0][:message]
        expect(error_message).to include('plan limit')
      end
    end

    context 'when no plan threshold exists' do
      before do
        OrganizationsPlansThreshold.destroy_all
      end

      it 'allows creation when no plan limits are set' do
        post '/v1/knowledge_base_files', valid_params, as_user(owner)
        
        expect_response(:created)
        expect(response_data['name']).to eq('New Knowledge Base File')
      end
    end
  end

  describe 'Authorization Edge Cases' do
    context 'when partner admin creates files' do
      it 'allows partner admin to create files' do
        post '/v1/knowledge_base_files', valid_params, as_user(partner_admin)
        
        expect_response(:created)
        expect(response_data['name']).to eq('New Knowledge Base File')
      end
    end

    context 'when team admin creates files' do
      it 'allows team admin to create files' do
        post '/v1/knowledge_base_files', valid_params, as_user(team_admin)
        
        expect_response(:created)
        expect(response_data['name']).to eq('New Knowledge Base File')
      end
    end

    context 'when regular member tries to create files' do
      it 'denies access to regular members' do
        post '/v1/knowledge_base_files', valid_params, as_user(regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when user from different organization tries to access' do
      it 'denies access to files from other organizations' do
        get "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(other_org_user)
        
        expect_error_response(:unauthorized)
      end

      it 'denies update access to files from other organizations' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", 
            { name: 'Updated Name' }, 
            as_user(other_org_user)
        
        expect_error_response(:unauthorized)
      end

      it 'denies delete access to files from other organizations' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(other_org_user)
        
        expect_error_response(:unauthorized)
      end
    end
  end

  describe 'PUT /v1/knowledge_base_files/:id - Update Edge Cases' do
    context 'when updating with invalid data' do
      it 'returns validation errors for empty name' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", 
            { name: '' }, 
            as_user(owner)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors for invalid file_url format' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", 
            { file_url: 'invalid-url' }, 
            as_user(owner)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when regular member tries to update' do
      it 'denies update access to regular members' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", 
            { name: 'Updated Name' }, 
            as_user(regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when updating non-existent file' do
      it 'returns not found error' do
        put '/v1/knowledge_base_files/99999', 
            { name: 'Updated Name' }, 
            as_user(owner)
        
        expect_error_response(:not_found)
      end
    end
  end

  describe 'DELETE /v1/knowledge_base_files/:id - Destroy Edge Cases' do
    context 'when regular member tries to delete' do
      it 'denies delete access to regular members' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when deleting non-existent file' do
      it 'returns not found error' do
        delete '/v1/knowledge_base_files/99999', {}, as_user(owner)
        
        expect_error_response(:not_found)
      end
    end

    context 'when file is already discarded' do
      before do
        knowledge_base_file.discard!
      end

      it 'returns not found error for discarded files' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(owner)
        
        expect_error_response(:not_found)
      end
    end
  end

  describe 'GET /v1/knowledge_base_files - List with Filters' do
    let!(:pdf_file) do
      create(:knowledge_base_file,
             organization: organization,
             name: 'PDF Document',
             filename: 'document.pdf',
             file_url: 'https://example.com/document.pdf')
    end
    let!(:doc_file) do
      create(:knowledge_base_file,
             organization: organization,
             name: 'Word Document',
             filename: 'document.docx',
             file_url: 'https://example.com/document.docx')
    end
    let!(:inactive_file) do
      create(:knowledge_base_file,
             organization: organization,
             name: 'Inactive File',
             discarded_at: Time.current)
    end

    context 'when filtering by search term' do
      it 'returns files matching search term' do
        get '/v1/knowledge_base_files', { search: 'PDF' }, as_user(owner)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['name']).to eq('PDF Document')
      end

      it 'returns empty results for non-matching search' do
        get '/v1/knowledge_base_files', { search: 'nonexistent' }, as_user(owner)
        
        expect_response(:ok)
        expect(response_data.size).to eq(0)
      end
    end

    context 'when filtering by file_type' do
      it 'filters by file extension' do
        get '/v1/knowledge_base_files', { file_type: 'pdf' }, as_user(owner)
        
        expect_response(:ok)
        file_names = response_data.map { |f| f['name'] }
        expect(file_names).to include('PDF Document', 'Test File')
        expect(file_names).not_to include('Word Document')
      end
    end

    context 'when filtering by active_only' do
      it 'returns only active files when active_only is true' do
        get '/v1/knowledge_base_files', { active_only: true }, as_user(owner)
        
        expect_response(:ok)
        file_names = response_data.map { |f| f['name'] }
        expect(file_names).not_to include('Inactive File')
      end

      it 'returns all files when active_only is false' do
        get '/v1/knowledge_base_files', { active_only: false }, as_user(owner)
        
        expect_response(:ok)
        # Should include active files but not discarded ones due to default scope
        file_names = response_data.map { |f| f['name'] }
        expect(file_names).not_to include('Inactive File')
      end
    end
  end

  describe 'Cross-Organization Access Prevention' do
    let!(:other_org_file) do
      create(:knowledge_base_file,
             organization: other_organization,
             name: 'Other Org File')
    end

    it 'does not show files from other organizations in list' do
      get '/v1/knowledge_base_files', {}, as_user(owner)
      
      expect_response(:ok)
      file_names = response_data.map { |f| f['name'] }
      expect(file_names).not_to include('Other Org File')
    end

    it 'prevents access to files from other organizations' do
      get "/v1/knowledge_base_files/#{other_org_file.id}", {}, as_user(owner)
      
      expect_error_response(:unauthorized)
    end
  end
end
