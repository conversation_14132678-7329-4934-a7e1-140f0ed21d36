# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::StoreItems CRUD Operations', type: :request do
  let!(:organization) { create(:organization) }
  let!(:admin) { create(:user) }
  let!(:super_admin_platform) { create(:user) }
  let!(:owner_platform) { create(:user) }
  let!(:regular_member) { create(:user) }

  let!(:admin_membership) { create(:membership, :admin, user: admin, organization: organization) }
  let!(:super_admin_platform_membership) do
    create(:membership, user: super_admin_platform, organization: organization, role: Membership.role_mappings['super_admin_platform'])
  end
  let!(:owner_platform_membership) do
    create(:membership, user: owner_platform, organization: organization, role: Membership.role_mappings['owner_platform'])
  end
  let!(:member_membership) { create(:membership, user: regular_member, organization: organization) }

  let(:valid_create_params) do
    {
      name: 'Premium Token Package',
      store_item_type: 'one_time_purchase_token',
      token_amounts: 10000,
      price: 99,
      price_decimal: 99,
      currency: 'USD'
    }
  end

  let(:valid_update_params) do
    {
      name: 'Updated Token Package',
      token_amounts: 15000,
      price: 149,
      price_decimal: 99
    }
  end

  describe 'POST /v1/store_items' do
    context 'when admin creates store item' do
      it 'successfully creates store item with all fields' do
        post '/v1/store_items', valid_create_params, as_user(admin)
        
        expect_response(:created)
        
        expect(response_data['name']).to eq('Premium Token Package')
        expect(response_data['store_item_type']).to eq('one_time_purchase_token')
        expect(response_data['token_amounts']).to eq(10000)
        expect(response_data['price']).to eq(99)
        expect(response_data['price_decimal']).to eq(99)
        expect(response_data['currency']).to eq('USD')
      end

      it 'creates store item with minimal required fields' do
        minimal_params = { name: 'Basic Package' }
        
        post '/v1/store_items', minimal_params, as_user(admin)
        
        expect_response(:created)
        expect(response_data['name']).to eq('Basic Package')
        expect(response_data['store_item_type']).to eq('one_time_purchase_token')
        expect(response_data['price']).to eq(0)
        expect(response_data['currency']).to eq('USD')
      end
    end

    context 'when super_admin_platform creates store item' do
      it 'allows super_admin_platform to create store items' do
        post '/v1/store_items', valid_create_params, as_user(super_admin_platform)
        
        expect_response(:created)
        expect(response_data['name']).to eq('Premium Token Package')
      end
    end

    context 'when owner_platform creates store item' do
      it 'allows owner_platform to create store items' do
        post '/v1/store_items', valid_create_params, as_user(owner_platform)
        
        expect_response(:created)
        expect(response_data['name']).to eq('Premium Token Package')
      end
    end

    context 'when regular member tries to create store item' do
      it 'denies access to regular members' do
        post '/v1/store_items', valid_create_params, as_user(regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with invalid parameters' do
      it 'returns validation error for missing name' do
        post '/v1/store_items', {}, as_user(admin)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation error for empty name' do
        post '/v1/store_items', { name: '' }, as_user(admin)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        post '/v1/store_items', valid_create_params
        
        expect_error_response(:unauthorized)
      end
    end
  end

  describe 'GET /v1/store_items/:id' do
    let!(:store_item) do
      StoreItem.create!(
        name: 'Test Store Item',
        store_item_type: 'one_time_purchase_token',
        token_amounts: 5000,
        price: 49,
        price_decimal: 99,
        currency: 'USD'
      )
    end

    context 'when user has valid membership' do
      it 'returns store item details' do
        get "/v1/store_items/#{store_item.id}", {}, as_user(admin)
        
        expect_response(:ok)
        
        expect(response_data['id']).to eq(store_item.id)
        expect(response_data['name']).to eq('Test Store Item')
        expect(response_data['token_amounts']).to eq(5000)
        expect(response_data['price']).to eq(49)
      end

      it 'allows regular member to view store items' do
        get "/v1/store_items/#{store_item.id}", {}, as_user(regular_member)
        
        expect_response(:ok)
        expect(response_data['name']).to eq('Test Store Item')
      end
    end

    context 'when store item does not exist' do
      it 'returns not found error' do
        get '/v1/store_items/99999', {}, as_user(admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get "/v1/store_items/#{store_item.id}", {}
        
        expect_error_response(:unauthorized)
      end
    end
  end

  describe 'GET /v1/store_items' do
    let!(:store_item_1) do
      StoreItem.create!(
        name: 'Basic Package',
        token_amounts: 1000,
        price: 10
      )
    end
    let!(:store_item_2) do
      StoreItem.create!(
        name: 'Premium Package',
        token_amounts: 10000,
        price: 100
      )
    end
    let!(:discarded_item) do
      StoreItem.create!(
        name: 'Discarded Package',
        token_amounts: 500,
        discarded_at: Time.current
      )
    end

    context 'when listing all store items' do
      it 'returns all active store items' do
        get '/v1/store_items', {}, as_user(admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(2)
        
        names = response_data.map { |item| item['name'] }
        expect(names).to contain_exactly('Basic Package', 'Premium Package')
      end

      it 'excludes discarded store items' do
        get '/v1/store_items', {}, as_user(admin)
        
        expect_response(:ok)
        names = response_data.map { |item| item['name'] }
        expect(names).not_to include('Discarded Package')
      end
    end

    context 'when filtering by search' do
      it 'filters store items by name' do
        get '/v1/store_items', { search: 'Basic' }, as_user(admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['name']).to eq('Basic Package')
      end

      it 'returns empty results for non-matching search' do
        get '/v1/store_items', { search: 'NonExistent' }, as_user(admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(0)
      end

      it 'handles case insensitive search' do
        get '/v1/store_items', { search: 'basic' }, as_user(admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
        expect(response_data.first['name']).to eq('Basic Package')
      end
    end

    context 'with pagination' do
      it 'handles page parameter' do
        get '/v1/store_items', { page: 1, per_page: 1 }, as_user(admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
      end

      it 'handles per_page parameter' do
        get '/v1/store_items', { per_page: 1 }, as_user(admin)
        
        expect_response(:ok)
        expect(response_data.size).to eq(1)
      end
    end

    context 'when regular member accesses index' do
      it 'allows regular member to view store items list' do
        get '/v1/store_items', {}, as_user(regular_member)
        
        expect_response(:ok)
        expect(response_data.size).to eq(2)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get '/v1/store_items', {}
        
        expect_error_response(:unauthorized)
      end
    end
  end

  describe 'PUT /v1/store_items/:id' do
    let!(:store_item) do
      StoreItem.create!(
        name: 'Original Package',
        store_item_type: 'one_time_purchase_token',
        token_amounts: 5000,
        price: 50,
        price_decimal: 0,
        currency: 'USD'
      )
    end

    context 'when admin updates store item' do
      it 'successfully updates store item fields' do
        put "/v1/store_items/#{store_item.id}", valid_update_params, as_user(admin)
        
        expect_response(:ok)
        
        expect(response_data['name']).to eq('Updated Token Package')
        expect(response_data['token_amounts']).to eq(15000)
        expect(response_data['price']).to eq(149)
        expect(response_data['price_decimal']).to eq(99)
      end

      it 'updates only specified fields' do
        partial_params = { name: 'Partially Updated' }
        
        put "/v1/store_items/#{store_item.id}", partial_params, as_user(admin)
        
        expect_response(:ok)
        
        expect(response_data['name']).to eq('Partially Updated')
        expect(response_data['token_amounts']).to eq(5000) # Unchanged
        expect(response_data['price']).to eq(50) # Unchanged
      end
    end

    context 'when super_admin_platform updates store item' do
      it 'allows super_admin_platform to update store items' do
        put "/v1/store_items/#{store_item.id}", valid_update_params, as_user(super_admin_platform)
        
        expect_response(:ok)
        expect(response_data['name']).to eq('Updated Token Package')
      end
    end

    context 'when regular member tries to update store item' do
      it 'denies access to regular members' do
        put "/v1/store_items/#{store_item.id}", valid_update_params, as_user(regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when store item does not exist' do
      it 'returns not found error' do
        put '/v1/store_items/99999', valid_update_params, as_user(admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        put "/v1/store_items/#{store_item.id}", valid_update_params
        
        expect_error_response(:unauthorized)
      end
    end
  end

  describe 'DELETE /v1/store_items/:id' do
    let!(:store_item) do
      StoreItem.create!(
        name: 'To Be Deleted',
        token_amounts: 1000,
        price: 10
      )
    end

    context 'when admin deletes store item' do
      it 'successfully soft deletes the store item' do
        delete "/v1/store_items/#{store_item.id}", {}, as_user(admin)
        
        expect_response(:ok)
        
        store_item.reload
        expect(store_item.discarded_at).not_to be_nil
      end

      it 'removes store item from default scope' do
        delete "/v1/store_items/#{store_item.id}", {}, as_user(admin)
        
        expect_response(:ok)
        
        # Verify item is no longer in default scope
        expect(StoreItem.find_by(id: store_item.id)).to be_nil
        
        # But exists in unscoped
        expect(StoreItem.unscoped.find_by(id: store_item.id)).to be_present
      end
    end

    context 'when super_admin_platform deletes store item' do
      it 'allows super_admin_platform to delete store items' do
        delete "/v1/store_items/#{store_item.id}", {}, as_user(super_admin_platform)
        
        expect_response(:ok)
        
        store_item.reload
        expect(store_item.discarded_at).not_to be_nil
      end
    end

    context 'when regular member tries to delete store item' do
      it 'denies access to regular members' do
        delete "/v1/store_items/#{store_item.id}", {}, as_user(regular_member)
        
        expect_error_response(:unauthorized)
        
        store_item.reload
        expect(store_item.discarded_at).to be_nil
      end
    end

    context 'when store item does not exist' do
      it 'returns not found error' do
        delete '/v1/store_items/99999', {}, as_user(admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when deleting already discarded store item' do
      before do
        store_item.discard!
      end

      it 'returns not found error for already discarded item' do
        delete "/v1/store_items/#{store_item.id}", {}, as_user(admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        delete "/v1/store_items/#{store_item.id}", {}
        
        expect_error_response(:unauthorized)
      end
    end
  end
end
