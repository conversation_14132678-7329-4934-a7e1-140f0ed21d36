# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ModelTemplatesController, type: :request do
  let!(:test_user) { create(:user, display_name: 'Test') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:membership_test_user) { create(:membership, user: test_user, organization: organization) }
  let!(:workspace_membership) { create(:workspaces_membership, membership: membership_test_user, workspace: workspace) }

  # Platform admin users
  let!(:platform_admin) { create(:user, display_name: 'Platform Admin') }
  let!(:membership_platform_admin) do
    create(:membership, :admin, user: platform_admin, organization: organization)
  end

  # Partner admin user
  let!(:partner_admin) { create(:user, display_name: 'Partner Admin', email: '<EMAIL>') }
  let!(:organization_created_by_partner) { create(:organization, name: 'Partner Org', created_by_id: partner_admin.id) }
  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: organization_created_by_partner)
  end

  let!(:model) { create(:model, name: 'ChatGPT gpt-4o', organization: organization) }
  let!(:bank_template) do
    create(:model_template, organization: organization, user: test_user, in_bank: true, verified: true)
  end
  let!(:non_bank_template) { create(:model_template, organization: organization, user: test_user, in_bank: false) }

  def add_to_bank(template_id, notes = nil, user)
    post "/v1/model_templates/#{template_id}/add_to_bank", { notes: notes }, as_user(user)
  end

  describe 'POST add_to_bank' do
    it 'add template to bank successfully' do
      add_to_bank(non_bank_template.id, 'Added to bank for testing', platform_admin)
      expect_response(:ok)

      duplicated_agent = ModelTemplate.find_by(parent_id: non_bank_template.id)
      expect(duplicated_agent).to be_present
      expect(duplicated_agent.id).to_not eq(non_bank_template.id)
      expect(duplicated_agent.in_bank).to be true
      expect(duplicated_agent.verified).to be true
      expect(duplicated_agent.bank_notes).to eq('Added to bank for testing')

      non_bank_template.reload
      expect(non_bank_template.in_bank).to be false
      expect(non_bank_template.bank_notes).to be_nil
    end

    it 'add template to bank without notes' do
      add_to_bank(non_bank_template.id, nil, platform_admin)
      expect_response(:ok)

      expect(response_data['in_bank']).to be true
      expect(response_data['verified']).to be true
      expect(response_data['bank_notes']).to be_nil
      expect(response_data['id']).not_to eq(non_bank_template.id)
      expect(response_data['parent_id']).to eq(non_bank_template.id)
    end

    it 'fail to add template that is already in bank' do
      add_to_bank(bank_template.id, 'Try to add again', platform_admin)
      expect_response(:unprocessable_entity)
    end

    it 'fail to add template to bank without proper authorization' do
      add_to_bank(non_bank_template.id, 'Unauthorized add', test_user)
      expect_response(:forbidden)
    end

    it 'partner admin can add template from their created organization' do
      new_template = ModelTemplate.create!(
        name: 'New Partner Template',
        description: 'A new template from partner org',
        max_tokens: 100,
        temperature: 1.0,
        model: 'openai/gpt-4o',
        instruction: 'You are a new partner template',
        prompt: 'New partner template prompt',
        placeholder: 'reference',
        organization_id: organization_created_by_partner.id,
        user: partner_admin
      )

      add_to_bank(new_template.id, 'Partner added this', partner_admin)
      expect_response(:ok)

      expect(response_data['in_bank']).to be true
      expect(response_data['verified']).to be true
    end

    it 'partner admin cannot add template from other organizations' do
      add_to_bank(non_bank_template.id, 'Try to add from other org', partner_admin)
      expect_response(:forbidden)
    end
  end
end
