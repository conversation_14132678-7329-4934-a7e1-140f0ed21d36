# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::ModelTemplate#duplicate', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: :password
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: :password
    )
  end

  let!(:organization) { create(:organization, name: 'Main Org') }

  let!(:organization2) { create(:organization, name: 'Second Org') }

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: -3 # admin role
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization.id,
      role: 3 # member role
    )
  end

  let!(:model_template) do
    ModelTemplate.create!(
      name: 'Test Template',
      description: 'This is a test template',
      max_tokens: 1000,
      model: 'openai/gpt-4o-mini',
      instruction: 'Test instruction',
      prompt: 'Test prompt',
      placeholder: 'Test placeholder',
      template_type: 'default',
      verified: true,
      organization_id: organization.id,
      user_id: admin.id,
      parent_id: nil
    )
  end

  let!(:parent_template) do
    ModelTemplate.create!(
      name: 'Parent Template',
      description: 'This is a parent template',
      max_tokens: 1000,
      model: 'openai/gpt-4o-mini',
      instruction: 'Parent instruction',
      prompt: 'Parent prompt',
      placeholder: 'Parent placeholder',
      template_type: 'default',
      verified: true,
      organization_id: organization.id,
      user_id: admin.id,
      parent_id: nil
    )
  end

  let!(:child_template) do
    ModelTemplate.create!(
      name: 'Child Template',
      description: 'This is a child template',
      max_tokens: 1000,
      model: 'openai/gpt-4o-mini',
      instruction: 'Child instruction',
      prompt: 'Child prompt',
      placeholder: 'Child placeholder',
      template_type: 'default',
      verified: true,
      organization_id: organization.id,
      user_id: admin.id,
      parent_id: parent_template.id
    )
  end

  def duplicate_template(template_id, which_user = admin)
    post "/v1/model_templates/#{template_id}/duplicate", {}, as_user(which_user)
  end

  def duplicate_to_organizations(template_id, organization_ids, which_user = admin)
    post "/v1/model_templates/#{template_id}/duplicate_to_organizations",
         { organization_ids: organization_ids, parent_id: template_id },
         as_user(which_user)
  end

  def update_parent_template(template_id, params, which_user = admin)
    put "/v1/parent_templates/#{template_id}", params, as_user(which_user)
  end

  def destroy_parent_template(template_id, which_user = admin)
    delete "/v1/parent_templates/#{template_id}", {}, as_user(which_user)
  end

  def duplicate_parent_template(template_id, which_user = admin)
    post "/v1/parent_templates/#{template_id}/duplicate", {}, as_user(which_user)
  end

  describe 'POST duplicate model template' do
    context 'when duplicating a regular template' do
      it 'creates a duplicate template with - Copy suffix' do
        duplicate_template(model_template.id, admin)
        expect_response(:ok)

        expect(response_data[:name]).to eq('Test Template - Copy')
        expect(response_data[:description]).to eq(model_template.description)
        expect(response_data[:max_tokens]).to eq(model_template.max_tokens)
        expect(response_data[:model]).to eq(model_template.model)
        expect(response_data[:instruction]).to eq(model_template.instruction)
        expect(response_data[:prompt]).to eq(model_template.prompt)
        expect(response_data[:placeholder]).to eq(model_template.placeholder)
        expect(response_data[:verified]).to eq(model_template.verified)
      end

      it 'creates a new template with different ID' do
        original_count = ModelTemplate.count
        duplicate_template(model_template.id, admin)
        expect_response(:ok)

        expect(ModelTemplate.count).to eq(original_count + 1)
        expect(response_data[:id]).not_to eq(model_template.id)
      end
    end

    context 'when user is not authorized' do
      it 'returns forbidden for non-admin users' do
        duplicate_template(model_template.id, member)
        expect_response(:forbidden)
      end
    end

    context 'when template does not exist' do
      it 'returns not found' do
        duplicate_template(99_999, admin)
        expect_response(:not_found)
      end
    end
  end
end
