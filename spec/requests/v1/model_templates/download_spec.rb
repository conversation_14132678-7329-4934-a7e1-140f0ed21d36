# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/model_templates/:id/download', type: :request do
  let!(:organization) { create(:organization) }
  let!(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: 'password'
    )
  end
  let!(:regular_user) do
    User.create!(
      display_name: 'Regular User',
      email: '<EMAIL>',
      password: 'password'
    )
  end
  let!(:partner_membership) do
    Membership.create!(
      user: partner_admin,
      organization: organization,
      role: Membership.role_mappings['partner_admin'],
      managed_organization_ids: [organization.id]
    )
  end
  let!(:regular_membership) do
    Membership.create!(
      user: regular_user,
      organization: organization,
      role: Membership.role_mappings['member']
    )
  end
  let!(:template_category) { create(:template_category, organization: organization) }
  let!(:model_template) do
    create(:model_template,
           user: partner_admin,
           organization: organization,
           template_category: template_category,
           name: 'Marketing Assistant',
           description: 'A helpful marketing template',
           prompt: 'You are a marketing expert. Help with marketing strategies.',
           instruction: 'System instruction for marketing',
           placeholder: 'Enter your marketing question here',
           max_tokens: 2000,
           temperature: 0.7,
           model: 'gpt-4o',
           verified: true,
           draft: false)
  end
  let!(:model_template_variable) do
    create(:model_template_variable,
           model_template: model_template,
           name: 'target_audience',
           description: 'Target audience for marketing')
  end
  let!(:model_template_in) do
    create(:model_template_in,
           model_template: model_template,
           name: 'campaign_type',
           description: 'Type of marketing campaign')
  end

  def download_template(id, which_user = partner_admin)
    get "/v1/model_templates/#{id}/download", {}, as_user(which_user).merge('Accept' => 'text/csv')
  end

  describe 'GET /v1/model_templates/:id/download' do
    context 'when user is partner admin with access' do
      it 'returns CSV file with template data' do
        download_template(model_template.id, partner_admin)
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('text/csv')
        expect(response.headers['Content-Disposition']).to include('attachment')
        expect(response.headers['Content-Disposition']).to include('marketing_assistant_')
        expect(response.headers['Content-Disposition']).to include('.csv')
      end

      it 'includes correct template information in CSV' do
        download_template(model_template.id, partner_admin)
        
        csv_content = response.body
        expect(csv_content).to include('Column Name,Column Value')
        expect(csv_content).to include('Name,Marketing Assistant')
        expect(csv_content).to include('Description,A helpful marketing template')
        expect(csv_content).to include('Max Tokens,2000')
        expect(csv_content).to include('Temperature,0.7')
      end

      it 'includes template variables in CSV' do
        download_template(model_template.id, partner_admin)
        
        csv_content = response.body
        expect(csv_content).to include('Variables,')
        expect(csv_content).to include('Variable Name - target_audience,Target audience for marketing')
      end

      it 'includes template inputs in CSV' do
        download_template(model_template.id, partner_admin)
        
        csv_content = response.body
        expect(csv_content).to include('Inputs,')
        expect(csv_content).to include('Input Name - campaign_type,Type of marketing campaign')
      end

      it 'includes rules and reference output in CSV' do
        download_template(model_template.id, partner_admin)
        
        csv_content = response.body
        expect(csv_content).to include('Rules,You are a marketing expert. Help with marketing strategies.')
        expect(csv_content).to include('Reference Output,Enter your marketing question here')
      end

      it 'generates filename with template name and timestamp' do
        freeze_time = Time.parse('2024-01-15 10:30:45')
        allow(Time).to receive(:now).and_return(freeze_time)
        
        download_template(model_template.id, partner_admin)
        
        expected_filename = 'marketing_assistant_20240115103045.csv'
        expect(response.headers['Content-Disposition']).to include(expected_filename)
      end
    end

    context 'when user is not partner admin' do
      it 'returns unauthorized error' do
        download_template(model_template.id, regular_user)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when template does not exist' do
      it 'returns not found error' do
        download_template(99999, partner_admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when partner admin does not have access to organization' do
      let!(:other_organization) { create(:organization) }
      let!(:other_template) do
        create(:model_template,
               organization: other_organization,
               name: 'Other Template')
      end

      it 'returns unauthorized error' do
        download_template(other_template.id, partner_admin)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when template has no variables or inputs' do
      let!(:simple_template) do
        create(:model_template,
               user: partner_admin,
               organization: organization,
               name: 'Simple Template',
               description: 'A simple template without variables or inputs')
      end

      it 'returns CSV with basic template data only' do
        download_template(simple_template.id, partner_admin)
        
        expect(response).to have_http_status(:ok)
        csv_content = response.body
        expect(csv_content).to include('Name,Simple Template')
        expect(csv_content).to include('Inputs,')
        expect(csv_content).to include('Variables,')
        # Should not include any variable or input entries
        expect(csv_content).not_to include('Variable Name -')
        expect(csv_content).not_to include('Input Name -')
      end
    end
  end
end
