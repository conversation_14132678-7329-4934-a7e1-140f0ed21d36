# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/model_templates/:id/system_prompt' do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, :admin, user:, organization:) }
  let!(:model_template) { create(:model_template) }
  let!(:model) { create(:model, model_template:, instruction: 'System prompt') }

  def system_prompt(id, which_user = user)
    get "/v1/model_templates/#{id}/system_prompt", {}, as_user(which_user)
  end

  it 'return 403 when accessed by non admin platform' do
    membership.update!(role: Membership.role_mappings['member'])
    system_prompt(model_template.id, user)

    expect_response(:forbidden)

    membership.update!(role: Membership.role_mappings['owner'])
    system_prompt(model_template.id, user)

    expect_response(:forbidden)
  end

  it 'returns the system prompt' do
    system_prompt(model_template.id)
    expect_response(:ok)

    expect(response_data['system_prompt']).to eq('System prompt')
  end
end
