require 'rails_helper'

RSpec.describe V1::ModelTemplatesController, type: :request do
  let(:test_user) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:test_user2) do
    User.create!(
      display_name: 'Test2',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization) { create(:organization, name: 'Test') }
  let(:workspace) do
    Workspace.create!(
      organization_id: organization.id,
      name: 'Test'
    )
  end
  let(:membership_test_user) do
    Membership.create!(
      user_id: test_user.id,
      organization_id: organization.id
    )
  end
  let(:workspace_membership) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user.id,
      workspace_id: workspace.id
    )
  end
  let(:membership_test_user2) do
    Membership.create!(
      user_id: test_user2.id,
      organization_id: organization.id
    )
  end
  let(:workspace_membership2) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user2.id,
      workspace_id: workspace.id
    )
  end

  let(:test_user_org2) do
    User.create!(
      display_name: 'Test Org2',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization2) { create(:organization, name: 'Test2') }
  let(:workspace2) do
    Workspace.create!(
      organization_id: organization2.id,
      name: 'Test2'
    )
  end
  let(:membership_test_user_org2) do
    Membership.create!(
      user_id: test_user_org2.id,
      organization_id: organization2.id
    )
  end
  let(:workspace_membership_org2) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user_org2.id,
      workspace_id: workspace2.id
    )
  end

  let(:model) do
    Model.create!(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098'
    )
  end
  let(:model_template) do
    ModelTemplate.create!(
      name: 'Test Template',
      description: 'Test Desc',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization.id,
      user: test_user
    )
  end
  let(:model_template2) do
    ModelTemplate.create!(
      name: 'Test Template2',
      description: 'Test Desc2',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization2.id,
      user: test_user_org2
    )
  end

  def list_template_comments(model_template_id, params, user)
    get "/v1/model_templates/#{model_template_id}/comments", params, as_user(user)
  end

  # need to comment before_create line in user model first
  describe 'GET list template comments' do
    before do
      # init data
      organization
      test_user
      test_user2
      workspace
      membership_test_user
      membership_test_user2
      workspace_membership
      workspace_membership2
      model
      model_template
      model_template2

      test_user_org2
      membership_test_user_org2
      workspace_membership_org2

      ModelRating.create!(
        rating: 5,
        user_id: test_user.id,
        model_template_id: model_template.id,
        comment: nil
      )

      ModelRating.create!(
        rating: 4,
        user_id: test_user2.id,
        model_template_id: model_template.id,
        comment: 'comment 2'
      )

      ModelRating.create!(
        rating: 3,
        user_id: test_user_org2.id,
        model_template_id: model_template2.id,
        comment: 'comment 3'
      )
    end

    it 'return list template comments from same organization' do
      list_template_comments(model_template.id, {}, test_user)
      expect_response(:ok)

      expect(response_data.size).to eq 2
    end
  end
end
