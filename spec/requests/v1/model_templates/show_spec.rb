# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::ModelTemplates#show', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:template_category) { create(:template_category, organization: organization) }
  let!(:model_template) do
    create(:model_template,
           user: user,
           organization: organization,
           template_category: template_category,
           name: 'Test Template',
           description: 'A helpful assistant template',
           prompt: 'You are a helpful assistant. Answer questions clearly.',
           instruction: 'System instruction for the template',
           placeholder: 'Enter your question here',
           template_type: 'default',
           max_tokens: 2000,
           temperature: 0.7,
           model: 'gpt-4o',
           verified: true,
           draft: false)
  end
  let!(:model_template_variable) do
    create(:model_template_variable,
           model_template: model_template,
           name: 'context',
           description: 'Context variable',
           weight: 'high')
  end
  let!(:model_template_in) do
    create(:model_template_in,
           model_template: model_template,
           name: 'input_text',
           description: 'Input text',
           weight: 'medium')
  end
  let!(:model_rating) do
    create(:model_rating,
           model_template: model_template,
           user: user,
           rating: 5,
           comment: 'Great template!')
  end

  def get_model_template(id, which_user = user)
    get "/v1/model_templates/#{id}", {}, as_user(which_user)
  end

  describe 'GET /v1/model_templates/:id' do
    context 'with valid model template id' do
      it 'returns model template details' do
        get_model_template(model_template.id, user)
        expect_response(:ok)

        expect(response_data['id']).to eq(model_template.id)
        expect(response_data['name']).to eq('Test Template')
        expect(response_data['description']).to eq('A helpful assistant template')
        expect(response_data['prompt']).to eq('You are a helpful assistant. Answer questions clearly.')
        expect(response_data['instruction']).to eq('System instruction for the template')
        expect(response_data['placeholder']).to eq('Enter your question here')
        expect(response_data['max_tokens']).to eq(2000)
        expect(response_data['temperature']).to eq(0.7)
        expect(response_data['model']).to eq('gpt-4o')
        expect(response_data['verified']).to eq(true)
        expect(response_data['draft']).to eq(false)
      end

      it 'returns model template with correct structure' do
        get_model_template(model_template.id, user)
        expect_response(
          :ok,
          data: {
            id: Integer,
            name: String,
            description: String,
            prompt: String,
            model: String,
            template_type: String,
            max_tokens: Integer,
            credits: Integer,
            temperature: Float,
            instruction: String,
            placeholder: String,
            category: Array,
            rating: Float,
            variables: Array,
            instruction_inputs: Array,
            template_category: Hash,
            number_of_used_times: Integer,
            number_of_comments: Integer,
            organization_id: Integer

          }
        )
      end

      it 'includes template category information' do
        get_model_template(model_template.id, user)
        expect_response(:ok)

        template_category_data = response_data['template_category']
        expect(template_category_data['id']).to eq(template_category.id)
        expect(template_category_data['name']).to eq(template_category.name)
      end

      it 'includes variables information' do
        get_model_template(model_template.id, user)
        expect_response(:ok)

        variables = response_data['variables']
        expect(variables).to be_present
        expect(variables.first['id']).to eq(model_template_variable.id)
        expect(variables.first['name']).to eq('context')
        expect(variables.first['description']).to eq('Context variable')
        expect(variables.first['weight']).to eq('high')
      end

      it 'includes instruction inputs information' do
        get_model_template(model_template.id, user)
        expect_response(:ok)

        instruction_inputs = response_data['instruction_inputs']
        expect(instruction_inputs).to be_present
        expect(instruction_inputs.first['id']).to eq(model_template_in.id)
        expect(instruction_inputs.first['name']).to eq('input_text')
        expect(instruction_inputs.first['description']).to eq('Input text')
        expect(instruction_inputs.first['weight']).to eq('medium')
      end

      it 'includes rating information' do
        get_model_template(model_template.id, user)
        expect_response(:ok)

        expect(response_data['rating']).to eq(5.0)
        expect(response_data['number_of_comments']).to eq(1)
      end

      it 'shows ownership correctly' do
        get_model_template(model_template.id, user)
        expect_response(:ok)

        expect(response_data['owned']).to eq(true)
        expect(response_data['organization_id']).to eq(organization.id)
      end
    end

    context 'with parent-child template relationships' do
      let!(:parent_template) do
        create(:model_template, :parent,
               user: user,
               organization: organization,
               name: 'Parent Template')
      end
      let!(:child_template) do
        create(:model_template, :child,
               parent: parent_template,
               user: user,
               organization: organization,
               name: 'Child Template')
      end

      it 'returns parent template with children information' do
        get_model_template(parent_template.id, user)
        expect_response(:ok)

        expect(response_data['is_parent']).to eq(true)
        expect(response_data['is_child']).to eq(false)
        expect(response_data['children']).to be_present
        expect(response_data['children'].first['id']).to eq(child_template.id)
        expect(response_data['children'].first['name']).to eq('Child Template')
      end

      it 'returns child template with parent information' do
        get_model_template(child_template.id, user)
        expect_response(:ok)

        expect(response_data['is_parent']).to eq(false)
        expect(response_data['is_child']).to eq(true)
        expect(response_data['parent_id']).to eq(parent_template.id)
      end
    end

    context 'with workflow template type' do
      let!(:agent_workflow) { create(:agent_workflow, organization: organization) }
      let!(:workflow_template) do
        create(:model_template,
               user: user,
               organization: organization,
               template_type: 'workflow',
               name: 'Workflow Template')
      end
      let!(:merger_workflow_node) do
        create(:agent_workflow_node,
               agent_workflow: agent_workflow,
               model_template: workflow_template,
               workflow_type: 'merger')
      end

      let!(:agent_workflow_node) do
        create(:agent_workflow_node,
               agent_workflow: agent_workflow,
               model_template: model_template,
               workflow_type: 'agent')
      end

      it 'returns workflow template with correct instruction inputs' do
        get_model_template(workflow_template.id, user)
        expect_response(:ok)

        expect(response_data['template_type']).to eq('workflow')
        expect(response_data['instruction_inputs']).to be_present
      end
    end

    context 'with bank template' do
      let!(:admin_user) { create(:user) }
      let!(:admin_membership) do
        create(:membership, user: admin_user, organization: organization, role: Membership.role_mappings[:admin])
      end
      let!(:bank_template) do
        create(:model_template,
               user: user,
               organization: organization,
               in_bank: true,
               bank_added_by: admin_user.id.to_s,
               bank_added_at: Time.current,
               bank_notes: 'High performing template')
      end

      it 'returns bank template with bank information' do
        get_model_template(bank_template.id, user)
        expect_response(:ok)

        expect(response_data['in_bank']).to eq(true)
        expect(response_data['bank_notes']).to eq('High performing template')
        expect(response_data['bank_added_at']).to be_present
      end
    end

    context 'with draft template' do
      let!(:draft_template) do
        create(:model_template,
               user: user,
               organization: organization,
               draft: true,
               verified: false)
      end

      it 'returns draft template' do
        get_model_template(draft_template.id, user)
        expect_response(:ok)

        expect(response_data['draft']).to eq(true)
        expect(response_data['verified']).to eq(false)
      end
    end

    context 'with organization prompt template' do
      let!(:org_template) do
        create(:model_template, :organization_prompt,
               user: user,
               organization: organization)
      end

      it 'returns organization prompt template' do
        get_model_template(org_template.id, user)
        expect_response(:ok)

        expect(response_data['organization_prompt']).to eq(true)
      end
    end

    context 'with invalid model template id' do
      it 'returns not found error' do
        get_model_template(99_999, user)
        expect_response(:not_found)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/model_templates/1', {}
        expect_response(:unauthorized)
      end
    end

    context 'with user from different organization' do
      let!(:other_user) { create(:user) }
      let!(:other_organization) { create(:organization) }
      let!(:other_membership) { create(:membership, user: other_user, organization: other_organization) }

      it 'returns forbidden error when accessing template from different organization' do
        get_model_template(model_template.id, other_user)
        expect_response(:forbidden)
      end
    end

    context 'with team admin user' do
      let!(:organization_team) { create(:organization_team, organization: organization) }
      let!(:team_admin) { create(:user) }
      let!(:team_admin_membership) do
        create(:membership,
               user: team_admin,
               organization: organization,
               role: Membership.role_mappings[:team_admin],
               organization_team_ids: [organization_team.id])
      end
      let!(:team_template) do
        create(:model_template,
               user: user,
               organization: organization,
               organization_team: organization_team)
      end

      it 'allows team admin to view team template' do
        get_model_template(team_template.id, team_admin)
        expect_response(:ok)

        expect(response_data['organization_team']['id']).to eq(organization_team.id)
        expect(response_data['organization_team']['name']).to eq(organization_team.name)
      end
    end
  end
end
