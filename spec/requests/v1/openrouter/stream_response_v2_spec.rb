# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::OpenaiController#stream_response_v2 with OpenRouter', type: :request do
  let(:test_user) { create(:user) }
  let(:organization) { create(:organization) }
  let(:membership) do
    create(:membership, user: test_user, organization: organization, role: Membership.role_mappings['owner'])
  end
  let(:workspace) { create(:workspace, organization: organization) }
  let(:workspace_membership) { create(:workspaces_membership, workspace: workspace, membership: membership) }
  let(:model) { create(:model, organization: organization) }
  let(:chat) { create(:chat, workspace: workspace, model: model) }

  let(:organizations_plans_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      remaining_monthly_credits: 1000,
      purchased_credits: 1000
    )
  end

  let(:request_params) do
    {
      query: 'Hello, how are you?',
      chat_id: chat.id
    }
  end

  before do
    # Create necessary records
    test_user
    organization
    membership
    workspace
    workspace_membership
    model
    chat
    organizations_plans_threshold

    # Mock the OpenAI client
    mock_client = instance_double(OpenAI::Client)
    allow(OpenAI::Client).to receive(:new).and_return(mock_client)

    # Mock the chat method to simulate streaming response
    allow(mock_client).to receive(:chat) do |params|
      stream_proc = params[:parameters][:stream]

      # Simulate streaming chunks
      stream_proc.call({
                         'id' => 'chunk-1',
                         'choices' => [{ 'delta' => { 'content' => 'Hello' } }]
                       }, 0)

      stream_proc.call({
                         'id' => 'chunk-2',
                         'choices' => [{ 'delta' => { 'content' => ', world!' } }]
                       }, 0)

      # Simulate usage data
      stream_proc.call({
                         'usage' => {
                           'prompt_tokens' => 10,
                           'completion_tokens' => 20,
                           'total_tokens' => 30
                         }
                       }, 0)
    end

    # Mock Rails credentials
    allow(Rails.application.credentials).to receive(:openrouter_key).and_return('test_key')
  end

  def stream_v2(params, which_user = test_user)
    post '/v1/stream_v2', params, as_user(which_user)
  end

  describe 'POST /v1/stream_v2' do
    it 'streams a response using OpenRouter service' do
      # Make the request
      stream_v2(request_params)

      # Expectations for streaming response
      expect_response(:ok)
      expect(response.headers['Content-Type']).to eq('text/event-stream')

      # Verify that messages were created
      expect(Message.where(chat_id: chat.id).count).to eq(2)

      user_message = Message.find_by(chat_id: chat.id, sender: 'user')
      expect(user_message).to be_present
      expect(user_message.content).to eq('Hello, how are you?')
      expect(user_message.tokens_used).to eq(10)

      assistant_message = Message.find_by(chat_id: chat.id, sender: 'assistant')
      expect(assistant_message).to be_present
      expect(assistant_message.content).to eq('Hello, world!')
      expect(assistant_message.tokens_used).to eq(20)

      # Verify that credits were deducted
      organizations_plans_threshold.reload
      expect(organizations_plans_threshold.remaining_monthly_credits).to be < 1000
    end

    context 'with web search enabled' do
      let(:request_params) do
        {
          query: 'What is the latest news?',
          chat_id: chat.id,
          web_search: true
        }
      end

      before do
        # Mock MetaInspector for web search results
        mock_meta_inspector = double('MetaInspector')
        allow(MetaInspector).to receive(:new).and_return(mock_meta_inspector)

        # Create a double for images that responds to best
        images_double = double('images')
        allow(images_double).to receive(:best).and_return('https://example.com/image.jpg')
        allow(mock_meta_inspector).to receive(:images).and_return(images_double)

        # Create a double for meta_tags that responds to dig
        meta_tags_double = double('meta_tags')
        allow(meta_tags_double).to receive(:dig).with('property', 'og:site_name').and_return(['Example Site'])
        allow(mock_meta_inspector).to receive(:meta_tags).and_return(meta_tags_double)

        allow(mock_meta_inspector).to receive(:title).and_return('Example Page')

        # Mock the OpenAI client to include annotations
        mock_client = instance_double(OpenAI::Client)
        allow(OpenAI::Client).to receive(:new).and_return(mock_client)

        allow(mock_client).to receive(:chat) do |params|
          stream_proc = params[:parameters][:stream]

          # Simulate annotations
          stream_proc.call({
                             'id' => 'chunk-1',
                             'choices' => [{
                               'delta' => {
                                 'annotations' => [{
                                   'type' => 'url_citation',
                                   'url_citation' => {
                                     'url' => 'https://example.com',
                                     'title' => 'Example Page',
                                     'content' => 'Example content',
                                     'start_index' => 0,
                                     'end_index' => 10
                                   }
                                 }]
                               }
                             }]
                           }, 0)

          # Simulate content
          stream_proc.call({
                             'id' => 'chunk-2',
                             'choices' => [{ 'delta' => { 'content' => 'According to the latest news' } }]
                           }, 0)

          # Simulate usage data
          stream_proc.call({
                             'usage' => {
                               'prompt_tokens' => 15,
                               'completion_tokens' => 25,
                               'total_tokens' => 40
                             }
                           }, 0)
        end
      end

      it 'streams a response with web search results' do
        post '/v1/stream_v2', request_params, as_user(test_user)

        expect_response(:ok)

        # Verify that web search results were created
        expect(WebSearchResult.count).to be > 0
        web_search_result = WebSearchResult.first
        expect(web_search_result.url).to eq('https://example.com')
        expect(web_search_result.title).to eq('Example Page')
      end
    end

    context 'when an error occurs' do
      before do
        # Mock the OpenAI client to raise an error
        mock_client = instance_double(OpenAI::Client)
        allow(OpenAI::Client).to receive(:new).and_return(mock_client)
        allow(mock_client).to receive(:chat).and_raise(StandardError.new('API error'))
      end

      it 'handles the error gracefully' do
        post '/v1/stream_v2', request_params, as_user(test_user)

        # The controller should rescue the error and return an error response
        expect(response).to have_http_status(:internal_server_error)
      end
    end
  end
end
