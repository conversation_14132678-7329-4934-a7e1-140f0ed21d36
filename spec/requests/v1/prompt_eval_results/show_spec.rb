# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::PromptEvalResults#show', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:model_bank) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:prompt_eval) do
    create(:prompt_eval, user: user, prompt: 'You are a helpful assistant', params: { temperature: 0.7 })
  end
  let!(:prompt_eval_result) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank,
           result_text: 'I am a helpful assistant that helps users with their questions. I can provide information, answer questions, and assist with various tasks. How can I help you today?',
           status: 'completed',
           response_raw: {
             usage: {
               prompt_tokens: 15,
               completion_tokens: 50,
               total_tokens: 65
             },
             choices: [
               {
                 message: {
                   content: 'I am a helpful assistant that helps users with their questions...'
                 }
               }
             ]
           })
  end

  def get_prompt_eval_result(id, which_user = user)
    get "/v1/prompt_eval_results/#{id}", {}, as_user(which_user)
  end

  describe 'GET /v1/prompt_eval_results/:id' do
    context 'with valid prompt eval result id' do
      it 'returns prompt eval result details' do
        get_prompt_eval_result(prompt_eval_result.id, user)
        expect_response(:ok)

        expect(response_data['id']).to eq(prompt_eval_result.id)
        expect(response_data['prompt_eval_id']).to eq(prompt_eval.id)
        expect(response_data['result_text']).to eq('I am a helpful assistant that helps users with their questions. I can provide information, answer questions, and assist with various tasks. How can I help you today?')
        expect(response_data['status']).to eq('completed')
        expect(response_data['error_message']).to be_nil
      end

      it 'returns prompt eval result with correct structure' do
        get_prompt_eval_result(prompt_eval_result.id, user)
        expect_response(:ok)

        expect(response_data).to include(
          'id',
          'prompt_eval_id',
          'model_bank',
          'result_text',
          'status',
          'error_message',
          'response_raw'
        )
      end

      it 'includes model bank information' do
        get_prompt_eval_result(prompt_eval_result.id, user)
        expect_response(:ok)

        model_bank_data = response_data['model_bank']
        expect(model_bank_data['id']).to eq(model_bank.id)
        expect(model_bank_data['name']).to eq('GPT-4o')
        expect(model_bank_data['code']).to eq('gpt-4o')
        expect(model_bank_data['input_rate']).to eq(0.01)
        expect(model_bank_data['output_rate']).to eq(0.03)
      end

      it 'includes response_raw data' do
        get_prompt_eval_result(prompt_eval_result.id, user)
        expect_response(:ok)

        response_raw = response_data['response_raw']
        expect(response_raw['usage']['total_tokens']).to eq(65)
        expect(response_raw['choices']).to be_present
      end
    end

    context 'with failed prompt eval result' do
      let!(:failed_result) do
        create(:prompt_eval_result,
               prompt_eval: prompt_eval,
               model_bank: model_bank,
               status: 'failed',
               error_message: 'API rate limit exceeded',
               response_raw: { error: 'rate_limit_exceeded' })
      end

      it 'returns failed result with error message' do
        get_prompt_eval_result(failed_result.id, user)
        expect_response(:ok)

        expect(response_data['status']).to eq('failed')
        expect(response_data['error_message']).to eq('API rate limit exceeded')
      end
    end

    context 'with pending prompt eval result' do
      let!(:pending_result) do
        create(:prompt_eval_result,
               prompt_eval: prompt_eval,
               model_bank: model_bank,
               status: 'pending')
      end

      it 'returns pending result' do
        get_prompt_eval_result(pending_result.id, user)
        expect_response(:ok)

        expect(response_data['status']).to eq('pending')
      end
    end

    context 'with invalid prompt eval result id' do
      it 'returns not found error' do
        get_prompt_eval_result(99_999, user)
        expect_response(:not_found)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/prompt_eval_results/1', {}
        expect_response(:unauthorized)
      end
    end
  end
end
