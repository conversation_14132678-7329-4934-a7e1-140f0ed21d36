# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::PromptEvalResults#index', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:model_bank_1) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:model_bank_2) do
    create(:model_bank, name: 'GPT-4o Mini', code: 'gpt-4o-mini', input_rate: 0.0003, output_rate: 0.0012)
  end

  let!(:model_template) do
    create(:model_template, organization:)
  end

  let!(:prompt_eval) do
    create(:prompt_eval, user: user, prompt: 'You are a helpful assistant', params: { temperature: 0.7 },
                         model_template:)
  end

  let!(:prompt_eval_result_1) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank_1,
           result_text: 'I am a helpful assistant that helps users with their questions',
           status: 'completed',
           response_raw: { usage: { total_tokens: 50 } })
  end
  let!(:prompt_eval_result_2) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank_2,
           result_text: 'I am a helpful assistant that provides assistance',
           status: 'completed',
           response_raw: { usage: { total_tokens: 45 } })
  end
  let!(:prompt_eval_result_pending) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank_1,
           status: 'pending')
  end

  def get_prompt_eval_results(params = {}, which_user = user)
    get '/v1/prompt_eval_results', params, as_user(which_user)
  end

  describe 'GET /v1/prompt_eval_results' do
    context 'with valid user' do
      it 'returns with correct format' do
        get_prompt_eval_results({}, user)
        expect_response(
          :ok,
          data: [
            {
              id: Integer,
              prompt_eval: {
                id: Integer,
                prompt: String,
                params: Hash,
                request_raw: Hash
              },
              model_bank: {
                id: Integer,
                name: String,
                code: String
              },
              result_text: String,
              status: String,
              response_raw: Hash
            }
          ]
        )
      end

      it 'returns list of prompt eval results' do
        get_prompt_eval_results({}, user)
        expect_response(:ok)

        expect(response_data.length).to eq(3)
        expect(response_data.first['prompt_eval_id']).to eq(prompt_eval.id)
        expect(response_data.first['result_text']).to be_present
      end

      it 'returns prompt eval results with correct structure' do
        get_prompt_eval_results({}, user)
        expect_response(:ok)

        result = response_data.first
        expect(result).to include(
          'id',
          'prompt_eval_id',
          'model_bank',
          'result_text',
          'status',
          'error_message',
          'response_raw'
        )
      end

      it 'includes model bank information in results' do
        get_prompt_eval_results({}, user)
        expect_response(:ok)

        result = response_data.first
        expect(result['model_bank']).to include(
          'id',
          'name',
          'code',
          'input_rate',
          'output_rate',
          'web_search_rate',
          'image_rate',
          'file_rate'
        )
      end
    end

    context 'with query parameters' do
      it 'filters by prompt_eval_id' do
        other_prompt_eval = create(:prompt_eval, user: user, prompt: 'Other prompt')
        create(:prompt_eval_result,
               prompt_eval: other_prompt_eval,
               model_bank: model_bank_1,
               result_text: 'Other result',
               status: 'completed')

        get_prompt_eval_results({ prompt_eval_id: prompt_eval.id }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(3)
        expect(response_data.map { |result| result['prompt_eval_id'] }).to all(eq(prompt_eval.id))
      end

      it 'filters by status' do
        get_prompt_eval_results({ status: 'completed' }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
        expect(response_data.map { |result| result['status'] }).to all(eq('completed'))
      end

      it 'filters by model_bank_id' do
        get_prompt_eval_results({ model_bank_id: model_bank_1.id }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
        expect(response_data.map { |result| result['model_bank']['id'] }).to all(eq(model_bank_1.id))
      end

      it 'handles pagination' do
        get_prompt_eval_results({ page: 1, per_page: 2 }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
      end

      it 'combines multiple filters' do
        get_prompt_eval_results({
                                  prompt_eval_id: prompt_eval.id,
                                  status: 'completed',
                                  model_bank_id: model_bank_1.id
                                }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(1)
        expect(response_data.first['status']).to eq('completed')
        expect(response_data.first['model_bank']['id']).to eq(model_bank_1.id)
      end

      it 'filters by model_template_id' do
        prompt_eval2 = create(:prompt_eval, user: user, prompt: 'Other prompt')
        prompt_eval_result_1.update!(prompt_eval: prompt_eval2)

        get_prompt_eval_results({ model_template_id: model_template.id }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get_prompt_eval_results({}, User.new)
        expect_response(:unauthorized)
      end
    end
  end
end
