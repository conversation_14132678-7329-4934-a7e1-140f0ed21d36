# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'PUT /v1/agent_workflows/:id', type: :request do
  let!(:admin) { create(:user) }
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization:) }
  let!(:user_membership) { create(:membership, :member, user:, organization:) }
  let!(:agent_workflow) { create(:agent_workflow, organization:) }

  let(:params) do
    {
      name: 'Test Agent Workflow',
      description: 'Test Agent Workflow Description'
    }
  end

  def update_workflow(id, params, which_user = user)
    put "/v1/agent_workflows/#{id}", params, as_user(which_user)
  end

  describe 'Update Agent Workflow' do
    context 'when agent_workflow not found' do
      it 'return error' do
        update_workflow(1000, params)
        expect_error_response(:not_found)
      end
    end

    context 'when role is admin platform' do
      it 'return updated' do
        update_workflow(agent_workflow.id, params, admin)
        expect_response(:ok)
      end
    end

    context 'when role is partner_admin' do
      before { user_membership.update!(role: Membership.role_mappings['partner_admin']) }

      it 'return error when try to update on other org' do
        agent_workflow.update!(organization_id: other_organization.id)
        update_workflow(agent_workflow.id, params, user)
        expect_error_response(:forbidden)
      end

      it 'return updated when try to update to other org when it created by itself' do
        other_organization.update!(created_by_id: user.id)
        agent_workflow.update!(organization_id: other_organization.id)

        update_workflow(agent_workflow.id, params, user)
        expect_response(:ok)
      end
    end

    context 'when role is member' do
      before { user_membership.update!(role: 'member') }

      it 'return error when try to update on other org' do
        agent_workflow.update!(organization_id: other_organization.id)
        update_workflow(agent_workflow.id, params, user)
        expect_error_response(:forbidden)
      end

      it 'return updated when try to update to other org when it created by itself' do
        other_organization.update!(created_by_id: user.id)
        update_workflow(agent_workflow.id, params, user)
        expect_response(:ok)
      end
    end

    it 'returns ok when update successfully' do
      update_workflow(agent_workflow.id, params)
      expect_response(:ok)

      agent_workflow.reload
      expect(agent_workflow.name).to eq(params[:name])
      expect(agent_workflow.description).to eq(params[:description])
    end
  end
end
