# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/agent_workflows/:id', type: :request do
  let!(:user) { create(:user) }
  let!(:admin) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:admin_membership) { create(:membership, user: admin, organization:) }
  let!(:user_membership) { create(:membership, user:, organization: other_organization) }

  let!(:model_template) { create(:model_template, organization:) }
  let!(:knowledge_base) { create(:knowledge_base_file, organization:) }
  let!(:model) { create(:model, model_template:, organization:) }

  let!(:model_template_input1) { create(:model_template_in, model_template:) }
  let!(:model_template_input2) { create(:model_template_in, model_template:) }

  let!(:agent_workflow) { create(:agent_workflow, created_by_id: user.id, organization:) }

  let!(:model_bank) { create(:model_bank) }

  let!(:agent_workflow_node_template) do
    create_list(
      :agent_workflow_node,
      2,
      workflow_type: 'template',
      agent_workflow:,
      model_template:,
      knowledge_base_file: knowledge_base,
      model_bank:
    )
  end

  def show(id, which_user = admin)
    get "/v1/agent_workflows/#{id}", {}, as_user(which_user)
  end

  describe 'Show agent workflow' do
    context 'when accessed by admin' do
      it 'returns the agent workflow' do
        show(agent_workflow.id)
        expect_response(:ok)

        expect(response_data).to be_present
      end
    end

    context 'when accessed by partner admin' do
      before do
        admin_membership.update!(role: Membership.role_mappings['partner_admin'])
        agent_workflow.update!(organization_id: other_organization.id)
      end

      it 'returns 403 when agent on other org' do
        show(agent_workflow.id)
        expect_response(:forbidden)
      end

      it 'returns 200 when it creted the org' do
        other_organization.update!(created_by_id: admin.id)

        show(agent_workflow.id)
        expect_response(:ok)
      end
    end

    it 'returns ok and correct data' do
      show(agent_workflow.id)

      expect_response(
        :ok,
        data: {
          id: Integer,
          name: String,
          nodes: [
            {
              order_level: Integer,
              workflow_type: String,
              model: {
                id: Integer,
                model: String
              },
              model_template: {
                id: Integer,
                name: String,
                instruction_inputs: [
                  {
                    name: String,
                    id: Integer
                  }
                ]
              },
              knowledge_base_file: {
                id: Integer
              },
              rules: String,
              reference_output: String
            }
          ]
        }
      )
    end
  end
end
