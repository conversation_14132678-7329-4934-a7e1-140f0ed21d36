# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'POST /v1/agent_workflows', type: :request do
  let!(:admin) { create(:user) }
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization:) }
  let!(:user_membership) { create(:membership, :member, user:, organization:) }
  let!(:model_bank) { create(:model_bank) }
  let(:model_template) { create(:model_template) }
  let(:model_template2) { create(:model_template) }

  let(:params) do
    {
      name: 'Test Agent Workflow',
      description: 'Test Agent Workflow Description',
      organization_id: organization.id
    }
  end

  def create_workflow(params, which_user = user)
    post '/v1/agent_workflows', params, as_user(which_user)
  end

  describe 'Create Agent Workflow' do
    context 'when request invalid' do
      it 'return error when name not present' do
        create_workflow(params.except(:name))
        expect_error_response(:unprocessable_entity)
      end

      it 'return error when organization_id not present' do
        create_workflow(params.except(:organization_id))
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when role is admin platform' do
      it 'return created' do
        create_workflow(params, admin)
        expect_response(:created)
      end
    end

    context 'when role is partner_admin' do
      before { user_membership.update!(role: Membership.role_mappings['partner_admin']) }

      it 'return error when try to create on other org' do
        params[:organization_id] = other_organization.id
        create_workflow(params, user)
        expect_error_response(:forbidden)
      end

      it 'return created when try to create to other org when it created by itself' do
        other_organization.update!(created_by_id: user.id)
        create_workflow(params, user)
        expect_response(:created)
      end
    end

    context 'when role is member' do
      it 'return error when try to create on other org' do
        params[:organization_id] = other_organization.id
        create_workflow(params, user)
        expect_error_response(:forbidden)
      end

      it 'return created when try to the assigned org' do
        create_workflow(params, user)
        expect_response(:created)
      end
    end

    context 'when model_template_ids present' do
      it 'returns create and create model_template merger and all other nodes' do
        params[:model_template_ids] = [model_template.id, model_template2.id]
        create_workflow(params, user)
        expect_response(:created)

        expect(AgentWorkflowNode.count).to eq 3

        agent_nodes = AgentWorkflowNode.where(workflow_type: 'agent')
        expect(agent_nodes.count).to eq 2
        expect(agent_nodes.map(&:name)).to match_array([model_template.name, model_template2.name])
        expect(agent_nodes.map(&:description)).to match_array([model_template.description, model_template2.description])
        expect(agent_nodes.map(&:order_level)).to match_array([0, 0])

        merger_node = AgentWorkflowNode.where(workflow_type: 'merger').first
        expect(merger_node).to be_present
        expect(merger_node.order_level).to eq 99

        model_template_merger = ModelTemplate.find(merger_node.model_template_id)
        expect(model_template_merger).to be_present
        expect(model_template_merger.template_type).to eq 'workflow'
        expect(model_template_merger.name).to eq params[:name]
        expect(model_template_merger.description).to eq params[:description]
        expect(model_template_merger.draft).to eq false

        model = Model.find_by(model_template_id: model_template_merger.id)
        expect(model).to be_present
        expect(model.name).to eq "#{model_template_merger.name} Merger"
      end
    end

    it 'returns error when organization not found' do
      params[:organization_id] = 999
      create_workflow(params, admin)
      expect_error_response(:not_found)
    end

    it 'return created and persisted' do
      create_workflow(params, admin)
      expect_response(
        :created,
        data: {
          id: Integer,
          name: String,
          description: String
        }
      )

      new_workflow = AgentWorkflow.find(response_data[:id])
      expect(new_workflow.created_by_id.to_i).to eq admin.id
      expect(new_workflow.organization_id).to eq params[:organization_id]
      expect(new_workflow.name).to eq params[:name]
      expect(new_workflow.description).to eq params[:description]
    end
  end
end
