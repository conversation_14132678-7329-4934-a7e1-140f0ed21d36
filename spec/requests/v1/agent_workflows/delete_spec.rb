# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'DELETE /v1/agent_workflows/:id', type: :request do
  let!(:admin) { create(:user) }
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:agent_workflow) { create(:agent_workflow, organization:) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization:) }
  let!(:user_membership) { create(:membership, :member, user:, organization:) }

  def delete_workflow(id, which_user = user)
    delete "/v1/agent_workflows/#{id}", {}, as_user(which_user)
  end

  describe 'Delete Agent Workflow' do
    it 'return 404 when not found' do
      delete_workflow('invalid_id')
      expect_error_response(:not_found)
    end

    context 'when role is admin platform' do
      it 'return 200' do
        delete_workflow(agent_workflow.id, admin)
        expect_response(:ok)
      end
    end

    context 'when role is partner_admin' do
      before do
        user_membership.update!(role: Membership.role_mappings['partner_admin'])
        agent_workflow.update!(organization_id: other_organization.id)
      end

      it 'return 403 when try to delete on agent of other org' do
        delete_workflow(agent_workflow.id, user)
        expect_error_response(:forbidden)
      end

      it 'return 200 when delete other org but created by it' do
        other_organization.update!(created_by_id: user.id)
        delete_workflow(agent_workflow.id, user)
        expect_response(:ok)
      end
    end

    context 'when accessed by member' do
      before do
        user_membership.update!(role: Membership.role_mappings['member'])
        agent_workflow.update!(organization_id: other_organization.id)
      end

      it 'return 403 when accessing other org' do
        delete_workflow(agent_workflow.id, user)
        expect_error_response(:forbidden)
      end
    end

    it 'return 200 and discarded' do
      id = agent_workflow.id
      delete_workflow(agent_workflow.id, user)
      expect_response(:ok)

      discarded_agent = AgentWorkflow.unscoped.find(id)
      expect(discarded_agent.discarded?).to be(true)
    end
  end
end
