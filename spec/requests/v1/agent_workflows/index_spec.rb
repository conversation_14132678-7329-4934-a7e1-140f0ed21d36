# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/agent_workflows' do
  let!(:user) { create(:user) }
  let!(:admin) { create(:user) }
  let!(:membership) { create(:membership, :member, user:, organization:) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization:) }
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:agent_workflow) { create_list(:agent_workflow, 2, organization:) }
  let!(:other_agent_workflow) { create_list(:agent_workflow, 3, organization: other_organization) }

  def index(query_params, which_user = admin)
    get '/v1/agent_workflows', query_params, as_user(which_user)
  end

  context 'when user is not authenticated' do
    it 'return 401' do
      get '/v1/agent_workflows'
      expect_error_response(:unauthorized)
    end
  end

  context 'when user is platform admin' do
    it 'return 200 with all agent workflow' do
      index({})
      expect_response(:ok)

      expect(response_data.size).to eq(agent_workflow.size + other_agent_workflow.size)
    end
  end

  context 'when user is partner_admin' do
    before { admin_membership.update!(role: Membership.role_mappings['partner_admin']) }

    it 'return 200 with only its own org' do
      index({})
      expect_response(:ok)

      expect(response_data.size).to eq(agent_workflow.size)
    end

    it 'return 200 with its created org agent workflow' do
      other_organization.update!(created_by_id: admin.id)
      index({})
      expect_response(:ok)

      expect(response_data.size).to eq(agent_workflow.size + other_agent_workflow.size)
    end
  end

  context 'when user is member' do
    it 'return 200 with only its own org' do
      index({}, user)
      expect_response(:ok)

      expect(response_data.size).to eq(agent_workflow.size)
    end
  end

  context 'when given params organization_id' do
    it 'return 200 with only given org' do
      index({ organization_id: other_organization.id })
      expect_response(:ok)

      expect(response_data.size).to eq(other_agent_workflow.size)

      index({ organization_id: organization.id })
      expect_response(:ok)

      expect(response_data.size).to eq(agent_workflow.size)
    end

    it 'return 200 with only their own org when accessed by member' do
      index({ organization_id: other_organization.id }, user)
      expect_response(:ok)

      expect(response_data.size).to eq(agent_workflow.size)
    end
  end

  context 'when given params search' do
    it 'return 200 with similar name' do
      agent_workflow.first.update!(name: 'test workflow')

      index({ search: 'test' })
      expect_response(:ok)

      expect(response_data.size).to eq(1)
    end
  end

  it 'returns 200 with correct data' do
    index({})
    expect_response(
      :ok,
      data: [
        {
          id: Integer,
          name: String,
          description: String
        }
      ]
    )
  end
end
