# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Users#update', type: :request do
  describe 'PUT /v1/users/:id' do
    let!(:user) { create(:user) }
    let!(:organization) { create(:organization) }
    let!(:membership) { create(:membership, user: user, organization: organization) }
    let!(:organizations_subscription) { create(:organizations_subscription, organization: organization) }
    let!(:organizations_plans_threshold) { create(:organizations_plans_threshold, organization: organization) }

    def update_user(id, params, which_user = user)
      put "/v1/users/#{id}", params, as_user(which_user)
    end

    context 'with valid parameters' do
      let(:update_params) do
        {
          display_name: 'Updated Name',
          photo_url: 'https://example.com/photo.jpg'
        }
      end

      it 'updates the user successfully' do
        update_user(user.id, update_params)
        expect_response(:ok)

        expect(response_data['display_name']).to eq('Updated Name')
        expect(response_data['photo_url']).to eq('https://example.com/photo.jpg')
      end
    end

    context 'when updating email' do
      let(:update_params) do
        {
          email: '<EMAIL>'
        }
      end

      it 'updates the email successfully' do
        update_user(user.id, update_params)
        expect_response(:ok)

        expect(response_data['email']).to eq('<EMAIL>')
      end
    end

    context 'when updating password' do
      let(:update_params) do
        {
          password: 'newpassword123',
          current_password: '12345678'
        }
      end

      it 'updates the password successfully' do
        update_user(user.id, update_params)
        expect_response(:ok)

        # Verify password was updated by checking user can authenticate with new password
        user.reload
        expect(user.authenticate('newpassword123')).to be_truthy
      end
    end

    context 'when updating password with wrong current password' do
      let(:update_params) do
        {
          password: 'newpassword123',
          current_password: 'wrongpassword'
        }
      end

      it 'returns an error' do
        update_user(user.id, update_params)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when updating another user\'s profile' do
      let!(:other_user) { create(:user) }
      let!(:other_membership) { create(:membership, user: other_user, organization: organization) }
      let(:update_params) do
        {
          display_name: 'Hacked Name'
        }
      end

      it 'updates the current user instead of the target user' do
        update_user(other_user.id, update_params)
        expect_response(:ok)

        # The service updates the current user (@user), not the target user
        expect(response_data['id']).to eq(user.id)
        expect(response_data['display_name']).to eq('Hacked Name')
      end
    end

    context 'with invalid parameters' do
      let!(:existing_user) { create(:user, email: '<EMAIL>') }
      let(:update_params) do
        {
          email: '<EMAIL>'
        }
      end

      it 'returns validation error for duplicate email' do
        update_user(user.id, update_params)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'with unauthorized user' do
      let(:update_params) do
        {
          display_name: 'Updated Name'
        }
      end

      it 'returns unauthorized error' do
        put "/v1/users/#{user.id}", update_params.to_json, { 'Content-Type' => 'application/json' }
        expect_response(:unauthorized)
      end
    end
  end
end
