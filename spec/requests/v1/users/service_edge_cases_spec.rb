# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Users Service Edge Cases', type: :request do
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:platform_admin) { create(:user) }
  let!(:super_admin_platform) { create(:user) }
  let!(:owner_platform) { create(:user) }
  let!(:regular_user) { create(:user) }
  let!(:other_user) { create(:user) }
  let!(:target_user) { create(:user) }

  let!(:platform_admin_membership) do
    create(:membership, user: platform_admin, organization: organization, role: Membership.role_mappings['admin'])
  end
  let!(:super_admin_platform_membership) do
    create(:membership, user: super_admin_platform, organization: organization, role: Membership.role_mappings['super_admin_platform'])
  end
  let!(:owner_platform_membership) do
    create(:membership, user: owner_platform, organization: organization, role: Membership.role_mappings['owner_platform'])
  end
  let!(:regular_membership) { create(:membership, user: regular_user, organization: organization) }
  let!(:other_membership) { create(:membership, user: other_user, organization: other_organization) }
  let!(:target_membership) { create(:membership, user: target_user, organization: organization) }

  let!(:organizations_subscription) { create(:organizations_subscription, organization: organization) }
  let!(:organizations_plans_threshold) { create(:organizations_plans_threshold, organization: organization) }

  describe 'GET /v1/users_get_by_email - Authorization Edge Cases' do
    context 'when super_admin_platform searches for users' do
      it 'allows super_admin_platform to get user by email' do
        get "/v1/users_get_by_email?email=#{target_user.email}", {}, as_user(super_admin_platform)
        
        expect_response(:ok)
        expect(response_data['email']).to eq(target_user.email)
      end
    end

    context 'when owner_platform searches for users' do
      it 'allows owner_platform to get user by email' do
        get "/v1/users_get_by_email?email=#{target_user.email}", {}, as_user(owner_platform)
        
        expect_response(:ok)
        expect(response_data['email']).to eq(target_user.email)
      end
    end

    context 'when platform admin searches for users' do
      it 'allows platform admin to get user by email' do
        get "/v1/users_get_by_email?email=#{target_user.email}", {}, as_user(platform_admin)
        
        expect_response(:ok)
        expect(response_data['email']).to eq(target_user.email)
      end
    end

    context 'when regular user tries to search' do
      it 'denies access to regular users' do
        get "/v1/users_get_by_email?email=#{target_user.email}", {}, as_user(regular_user)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when searching for non-existent email' do
      it 'returns not found error' do
        get "/v1/users_get_by_email?email=<EMAIL>", {}, as_user(platform_admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when email parameter is missing' do
      it 'returns error for missing email parameter' do
        get "/v1/users_get_by_email", {}, as_user(platform_admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when email parameter is empty' do
      it 'returns not found error for empty email' do
        get "/v1/users_get_by_email?email=", {}, as_user(platform_admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'with case sensitivity' do
      it 'handles case insensitive email search' do
        get "/v1/users_get_by_email?email=#{target_user.email.upcase}", {}, as_user(platform_admin)
        
        expect_response(:ok)
        expect(response_data['email']).to eq(target_user.email)
      end
    end
  end

  describe 'PUT /v1/users/:id - Update Edge Cases' do
    context 'when updating password with current password' do
      let(:update_params) do
        {
          password: 'new_password123',
          current_password: 'password123'
        }
      end

      it 'successfully updates password with correct current password' do
        put "/v1/users/#{regular_user.id}", update_params, as_user(regular_user)
        
        expect_response(:ok)
        
        # Verify password was changed
        regular_user.reload
        expect(regular_user.authenticate('new_password123')).to be_truthy
      end

      it 'rejects password update with incorrect current password' do
        invalid_params = update_params.merge(current_password: 'wrong_password')
        
        put "/v1/users/#{regular_user.id}", invalid_params, as_user(regular_user)
        
        expect_error_response(:unprocessable_entity)
        error_message = response_body[:errors][0][:message]
        expect(error_message).to include('Invalid current password')
      end
    end

    context 'when updating other user profile fields' do
      let(:update_params) do
        {
          display_name: 'Updated Name',
          email: '<EMAIL>'
        }
      end

      it 'successfully updates profile fields' do
        put "/v1/users/#{regular_user.id}", update_params, as_user(regular_user)
        
        expect_response(:ok)
        expect(response_data['display_name']).to eq('Updated Name')
        expect(response_data['email']).to eq('<EMAIL>')
      end

      it 'prevents updating another user\'s profile' do
        put "/v1/users/#{other_user.id}", update_params, as_user(regular_user)
        
        expect_error_response(:forbidden)
      end
    end

    context 'with invalid update parameters' do
      it 'returns validation errors for invalid email format' do
        put "/v1/users/#{regular_user.id}", { email: 'invalid-email' }, as_user(regular_user)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors for empty display name' do
        put "/v1/users/#{regular_user.id}", { display_name: '' }, as_user(regular_user)
        
        expect_error_response(:unprocessable_entity)
      end
    end
  end

  describe 'POST /v1/users/:id/change_password - Authorization Hierarchy' do
    let(:change_password_params) do
      {
        new_password: 'new_secure_password123'
      }
    end

    context 'when platform admin changes regular user password' do
      it 'allows platform admin to change regular user password' do
        post "/v1/users/#{target_user.id}/change_password", change_password_params, as_user(platform_admin)
        
        expect_response(:ok)
        
        # Verify password was changed
        target_user.reload
        expect(target_user.authenticate('new_secure_password123')).to be_truthy
      end
    end

    context 'when super_admin_platform changes platform admin password' do
      it 'allows super_admin_platform to change platform admin password' do
        post "/v1/users/#{platform_admin.id}/change_password", change_password_params, as_user(super_admin_platform)
        
        expect_response(:ok)
        
        platform_admin.reload
        expect(platform_admin.authenticate('new_secure_password123')).to be_truthy
      end
    end

    context 'when owner_platform changes any user password' do
      it 'allows owner_platform to change super_admin_platform password' do
        post "/v1/users/#{super_admin_platform.id}/change_password", change_password_params, as_user(owner_platform)
        
        expect_response(:ok)
        
        super_admin_platform.reload
        expect(super_admin_platform.authenticate('new_secure_password123')).to be_truthy
      end
    end

    context 'when user changes own password' do
      it 'allows user to change their own password' do
        post "/v1/users/#{regular_user.id}/change_password", change_password_params, as_user(regular_user)
        
        expect_response(:ok)
        
        regular_user.reload
        expect(regular_user.authenticate('new_secure_password123')).to be_truthy
      end
    end

    context 'when lower role tries to change higher role password' do
      it 'prevents regular user from changing platform admin password' do
        post "/v1/users/#{platform_admin.id}/change_password", change_password_params, as_user(regular_user)
        
        expect_error_response(:unauthorized)
      end

      it 'prevents platform admin from changing super_admin_platform password' do
        post "/v1/users/#{super_admin_platform.id}/change_password", change_password_params, as_user(platform_admin)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when user has no membership' do
      let!(:no_membership_user) { create(:user) }

      it 'allows changing password for user with no membership' do
        post "/v1/users/#{no_membership_user.id}/change_password", change_password_params, as_user(platform_admin)
        
        expect_response(:ok)
        
        no_membership_user.reload
        expect(no_membership_user.authenticate('new_secure_password123')).to be_truthy
      end
    end

    context 'with invalid password parameters' do
      it 'returns validation error for empty password' do
        post "/v1/users/#{target_user.id}/change_password", { new_password: '' }, as_user(platform_admin)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation error for short password' do
        post "/v1/users/#{target_user.id}/change_password", { new_password: '123' }, as_user(platform_admin)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when target user does not exist' do
      it 'returns not found error' do
        post "/v1/users/99999/change_password", change_password_params, as_user(platform_admin)
        
        expect_error_response(:not_found)
      end
    end
  end

  describe 'GET /v1/users/:id - Show Edge Cases' do
    context 'when user has multiple organization memberships' do
      let!(:second_membership) { create(:membership, user: regular_user, organization: other_organization) }

      it 'returns user with primary organization information' do
        get "/v1/users/#{regular_user.id}", {}, as_user(regular_user)
        
        expect_response(:ok)
        expect(response_data['organization']['id']).to eq(organization.id)
      end
    end

    context 'when user has no organization membership' do
      let!(:no_membership_user) { create(:user) }

      it 'returns error for user without organization' do
        get "/v1/users/#{no_membership_user.id}", {}, as_user(no_membership_user)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when requesting non-existent user' do
      it 'returns not found error' do
        get "/v1/users/99999", {}, as_user(regular_user)
        
        expect_error_response(:not_found)
      end
    end
  end
end
