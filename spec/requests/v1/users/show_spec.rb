# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Users#show', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:plan_threshold) { create(:organizations_plans_threshold, organization: organization) }
  let!(:subscription) { create(:organizations_subscription, organization: organization) }

  def get_user(id, which_user = user)
    get "/v1/users/#{id}", {}, as_user(which_user)
  end

  describe 'GET /v1/users/:id' do
    context 'with valid user' do
      it 'returns the user details' do
        get_user(user.id)
        expect_response(:ok)

        expect(response_data['id']).to eq(user.id)
        expect(response_data['email']).to eq(user.email)
        expect(response_data['organization']['id']).to eq(organization.id)
      end
    end

    context 'when requesting another user\'s profile' do
      let!(:other_user) { create(:user) }

      it 'returns an error' do
        get_user(other_user.id)
        expect_error_response(:forbidden)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get "/v1/users/#{user.id}", {}, {}
        expect_response(:unauthorized)
      end
    end
  end
end