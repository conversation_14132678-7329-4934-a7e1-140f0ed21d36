# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Users#change_password', type: :request do
  describe 'POST /v1/users/:id/change_password' do
    let!(:platform_admin_user) { create(:user) }
    let!(:organization) { create(:organization) }
    let!(:platform_admin_membership) { create(:membership, user: platform_admin_user, organization: organization, role: -3) } # admin role (platform admin)
    let!(:organizations_subscription) { create(:organizations_subscription, organization: organization) }
    let!(:organizations_plans_threshold) { create(:organizations_plans_threshold, organization: organization) }

    def change_password(user_id, params, which_user = platform_admin_user)
      post "/v1/users/#{user_id}/change_password", params, as_user(which_user)
    end

    context 'when platform admin changes own password' do
      let(:change_params) do
        {
          new_password: 'newpassword123'
        }
      end

      it 'changes the password successfully' do
        change_password(platform_admin_user.id, change_params)
        expect_response(:ok)

        # Verify password was changed by checking user can authenticate with new password
        platform_admin_user.reload
        expect(platform_admin_user.authenticate('newpassword123')).to be_truthy
      end
    end

    context 'when platform admin changes another user password' do
      let!(:target_user) { create(:user) }
      let!(:target_membership) { create(:membership, user: target_user, organization: organization, role: 3) } # member role
      let(:change_params) do
        {
          new_password: 'newpassword123'
        }
      end

      it 'changes the target user password successfully' do
        change_password(target_user.id, change_params)
        expect_response(:ok)

        # Verify target user password was changed
        target_user.reload
        expect(target_user.authenticate('newpassword123')).to be_truthy
      end
    end

    context 'when non-platform admin tries to change password' do
      let!(:member_user) { create(:user) }
      let!(:member_membership) { create(:membership, user: member_user, organization: organization, role: 3) } # member role
      let(:change_params) do
        {
          new_password: 'newpassword123'
        }
      end

      it 'returns forbidden error' do
        change_password(member_user.id, change_params, member_user)
        expect_error_response(:forbidden)
      end
    end

    context 'when platform admin tries to change higher privilege user password' do
      let!(:super_admin_user) { create(:user) }
      let!(:super_admin_membership) { create(:membership, user: super_admin_user, organization: organization, role: -4) } # super_admin_platform role
      let!(:admin_user) { create(:user) }
      let!(:admin_membership) { create(:membership, user: admin_user, organization: organization, role: -3) } # admin role
      let(:change_params) do
        {
          new_password: 'newpassword123'
        }
      end

      it 'returns forbidden error when admin tries to change super_admin password' do
        change_password(super_admin_user.id, change_params, admin_user)
        expect_error_response(:forbidden)
      end
    end

    context 'when platform admin tries to change same privilege user password' do
      let!(:another_admin_user) { create(:user) }
      let!(:another_admin_membership) { create(:membership, user: another_admin_user, organization: organization, role: -3) } # admin role
      let(:change_params) do
        {
          new_password: 'newpassword123'
        }
      end

      it 'returns forbidden error when admin tries to change another admin password' do
        change_password(another_admin_user.id, change_params, platform_admin_user)
        expect_error_response(:forbidden)
      end
    end

    context 'with missing new_password parameter' do
      let(:change_params) do
        {
          notification_status: true
        }
      end

      it 'returns validation error' do
        change_password(platform_admin_user.id, change_params)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'with unauthorized user' do
      let(:change_params) do
        {
          new_password: 'newpassword123'
        }
      end

      it 'returns unauthorized error' do
        post "/v1/users/#{platform_admin_user.id}/change_password", change_params
        expect_response(:unauthorized)
      end
    end
  end
end
