# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Users#get_by_email', type: :request do
  describe 'GET /v1/users_get_by_email' do
    let!(:user) { create(:user) }
    let!(:organization) { create(:organization) }
    let!(:membership) { create(:membership, :super_admin_platform, user: user, organization: organization) }
    let!(:organizations_subscription) { create(:organizations_subscription, organization: organization) }
    let!(:organizations_plans_threshold) { create(:organizations_plans_threshold, organization: organization) }

    def get_user_by_email(email)
      get "/v1/users_get_by_email?email=#{email}", {}, as_user(user)
    end

    context 'with valid email' do
      let!(:target_user) { create(:user, email: '<EMAIL>') }
      let!(:target_membership) { create(:membership, user: target_user, organization: organization) }

      it 'returns the user' do
        get_user_by_email(target_user.email)
        expect_response(:ok)
        expect(response_data['id']).to eq(target_user.id)
        expect(response_data['email']).to eq(target_user.email)
      end
    end

    context 'with non-existent email' do
      it 'returns not found error' do
        get_user_by_email('<EMAIL>')
        expect_error_response(:not_found)
      end
    end

    context 'with missing email parameter' do
      it 'returns not found error' do
        get '/v1/users_get_by_email', {}, as_user(user)
        expect_error_response(:not_found)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/users_get_by_email?email=<EMAIL>', {}, {}
        expect_response(:unauthorized)
      end
    end
  end
end