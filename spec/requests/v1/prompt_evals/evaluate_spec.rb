# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::PromptEvals#evaluate', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:model_bank_1) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:model_bank_2) do
    create(:model_bank, name: 'GPT-4o Mini', code: 'gpt-4o-mini', input_rate: 0.0003, output_rate: 0.0012)
  end

  let!(:model_template) do
    create(:model_template, organization:)
  end

  let(:valid_params) do
    {
      model_bank_ids: [model_bank_1.id, model_bank_2.id],
      prompt: 'You are a helpful assistant that helps users with their questions',
      model_template_id: model_template.id,
      params: {
        temperature: 0.7,
        max_tokens: 1000
      }
    }
  end

  def evaluate_prompt(params, which_user = user)
    post '/v1/prompt_evals/evaluate', params, as_user(which_user)
  end

  describe 'POST /v1/prompt_evals/evaluate' do
    context 'with valid parameters' do
      it 'creates a new prompt eval and results' do
        evaluate_prompt(valid_params, user)
        expect_response(:created)

        expect(response_data['id']).to be_present
        expect(response_data['prompt']).to eq('You are a helpful assistant that helps users with their questions')
        expect(response_data['params']).to eq({ 'temperature' => 0.7, 'max_tokens' => 1000 })
        expect(response_data['user_id']).to eq(user.id.to_s)
      end

      it 'creates prompt eval results for each model bank' do
        evaluate_prompt(valid_params, user)
        expect_response(:created)

        prompt_eval_id = response_data['id']
        prompt_eval_results = PromptEvalResult.where(prompt_eval_id: prompt_eval_id)

        expect(prompt_eval_results.count).to eq(2)
        expect(prompt_eval_results.map(&:model_bank_id)).to match_array([model_bank_1.id, model_bank_2.id])
        expect(prompt_eval_results.map(&:status)).to all(eq('pending'))
      end

      it 'creates prompt eval with correct user_id' do
        evaluate_prompt(valid_params, user)
        expect_response(:created)

        prompt_eval = PromptEval.find(response_data['id'])
        expect(prompt_eval.user_id).to eq(user.id.to_s)
      end
    end

    context 'with invalid parameters' do
      it 'returns error when model_bank_ids is missing' do
        invalid_params = valid_params.except(:model_bank_ids)
        evaluate_prompt(invalid_params, user)
        expect_response(:unprocessable_entity)
      end

      it 'returns error when prompt is missing' do
        invalid_params = valid_params.except(:prompt)
        evaluate_prompt(invalid_params, user)
        expect_response(:unprocessable_entity)
      end

      it 'returns error when params is missing' do
        invalid_params = valid_params.except(:params)
        evaluate_prompt(invalid_params, user)
        expect_response(:unprocessable_entity)
      end

      it 'returns error when model_bank_ids is empty' do
        invalid_params = valid_params.merge(model_bank_ids: [])
        evaluate_prompt(invalid_params, user)
        expect_response(:unprocessable_entity)
      end

      it 'returns error when prompt is empty' do
        invalid_params = valid_params.merge(prompt: '')
        evaluate_prompt(invalid_params, user)
        expect_response(:unprocessable_entity)
      end
    end

    context 'with non-existent model bank ids' do
      it 'creates prompt eval but skips non-existent model banks' do
        invalid_params = valid_params.merge(model_bank_ids: [99_999, model_bank_1.id])
        evaluate_prompt(invalid_params, user)
        expect_response(:created)

        prompt_eval_id = response_data['id']
        prompt_eval_results = PromptEvalResult.where(prompt_eval_id: prompt_eval_id)

        expect(prompt_eval_results.count).to eq(1)
        expect(prompt_eval_results.first.model_bank_id).to eq(model_bank_1.id)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/prompt_evals/evaluate', {}
        expect_response(:unauthorized)
      end
    end

    context 'with single model bank' do
      it 'creates prompt eval with single result' do
        single_bank_params = valid_params.merge(model_bank_ids: [model_bank_1.id])
        evaluate_prompt(single_bank_params, user)
        expect_response(:created)

        prompt_eval_id = response_data['id']
        prompt_eval_results = PromptEvalResult.where(prompt_eval_id: prompt_eval_id)

        expect(prompt_eval_results.count).to eq(1)
        expect(prompt_eval_results.first.model_bank_id).to eq(model_bank_1.id)
      end
    end
  end
end
