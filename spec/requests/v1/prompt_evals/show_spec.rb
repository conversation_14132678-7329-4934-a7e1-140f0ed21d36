# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::PromptEvals#show', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:model_bank) { create(:model_bank, name: 'GPT-4o', code: 'gpt-4o', input_rate: 0.01, output_rate: 0.03) }
  let!(:prompt_eval) do
    create(:prompt_eval, user: user, prompt: 'You are a helpful assistant', params: { temperature: 0.7 })
  end
  let!(:prompt_eval_result_completed) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank,
           result_text: 'I am a helpful assistant',
           status: 'completed',
           response_raw: { usage: { total_tokens: 50 } })
  end
  let!(:prompt_eval_result_pending) do
    create(:prompt_eval_result,
           prompt_eval: prompt_eval,
           model_bank: model_bank,
           status: 'pending')
  end

  def get_prompt_eval(id, which_user = user)
    get "/v1/prompt_evals/#{id}", {}, as_user(which_user)
  end

  describe 'GET /v1/prompt_evals/:id' do
    context 'with valid prompt eval id' do
      it 'returns prompt eval with completed status' do
        PromptEvalResult.where(prompt_eval_id: prompt_eval.id).update_all(status: 'completed')

        get_prompt_eval(prompt_eval.id, user)
        expect_response(:ok)

        expect(response_data['id']).to eq(prompt_eval.id)
        expect(response_data['prompt']).to eq('You are a helpful assistant')
        expect(response_data['status']).to eq('completed')
      end

      it 'returns prompt eval with pending status when results are pending' do
        # Remove completed result to test pending status
        prompt_eval_result_completed.destroy

        get_prompt_eval(prompt_eval.id, user)
        expect_response(:ok)

        expect(response_data['status']).to eq('pending')
      end

      it 'returns prompt eval with failed status when results have errors' do
        create(:prompt_eval_result, prompt_eval: prompt_eval, model_bank: model_bank, status: 'failed')

        get_prompt_eval(prompt_eval.id, user)
        expect_response(:ok)

        expect(response_data['status']).to eq('failed')
      end

      it 'returns prompt eval with correct structure' do
        get_prompt_eval(prompt_eval.id, user)
        expect_response(
          :ok,
          data: {
            id: Integer,
            prompt: String,
            params: Hash,
            request_raw: Hash
          }
        )
      end
    end

    context 'with invalid prompt eval id' do
      it 'returns not found error' do
        get_prompt_eval(99_999, user)
        expect_response(:not_found)
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/prompt_evals/1', {}
        expect_response(:unauthorized)
      end
    end
  end
end
