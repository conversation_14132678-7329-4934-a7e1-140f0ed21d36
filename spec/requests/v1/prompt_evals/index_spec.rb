# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::PromptEvals#index', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:prompt_eval_1) do
    create(:prompt_eval, user: user, prompt: 'You are a helpful assistant', params: { temperature: 0.7 })
  end
  let!(:prompt_eval_2) do
    create(:prompt_eval, user: user, prompt: 'Explain quantum computing', params: { max_tokens: 1000 },
                         request_raw: { test: 'data' })
  end

  def get_prompt_evals(params = {}, which_user = user)
    get '/v1/prompt_evals', params, as_user(which_user)
  end

  describe 'GET /v1/prompt_evals' do
    context 'with valid user' do
      it 'returns list of prompt evals' do
        get_prompt_evals({}, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
        # Check that both prompts are present (order may vary)
        prompts = response_data.map { |eval| eval['prompt'] }
        expect(prompts).to include('You are a helpful assistant')
        expect(prompts).to include('Explain quantum computing')
      end

      it 'returns prompt evals with correct structure' do
        get_prompt_evals({}, user)
        expect_response(:ok)

        prompt_eval = response_data.first
        expect(prompt_eval).to include(
          'id',
          'prompt',
          'params',
          'request_raw',
          'user_id'
        )
      end
    end

    context 'with query parameters' do
      it 'handles pagination' do
        get_prompt_evals({ page: 1, per_page: 1 }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(1)
      end

      it 'filters by user_id' do
        other_user = create(:user)
        create(:prompt_eval, user: other_user, prompt: 'Other user prompt')

        get_prompt_evals({ user_id: user.id }, user)
        expect_response(:ok)

        expect(response_data.length).to eq(2)
        expect(response_data.map { |eval| eval['user_id'] }).to all(eq(user.id.to_s))
      end
    end

    context 'with unauthorized user' do
      it 'returns unauthorized error' do
        get '/v1/prompt_evals', {}, {}
        expect_response(:unauthorized)
      end
    end
  end
end
