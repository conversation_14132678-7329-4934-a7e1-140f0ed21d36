# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/citations/:id', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }
  let!(:workspace) { create(:workspace, organization: organization) }
  let!(:workspace_membership) { create(:workspaces_membership, workspace: workspace, membership: membership) }
  let!(:model) { create(:model, organization: organization) }
  let!(:chat) do
    create(:chat,
           workspace: workspace,
           model: model,
           workspace_membership_workspace_id: workspace.id,
           workspace_membership_membership_id: membership.id)
  end
  let!(:message) { create(:message, chat: chat) }
  let!(:web_search_result) do
    create(:web_search_result,
           message: message,
           url: 'https://example.com/article',
           title: 'Example Article',
           content: 'This is an example article content for testing citations.',
           start_index: 0,
           end_index: 50)
  end

  def get_citation(id, which_user = user)
    get "/v1/citations/#{id}", {}, as_user(which_user)
  end

  describe 'GET /v1/citations/:id' do
    context 'when user owns the chat' do
      it 'returns citation data successfully' do
        get_citation(web_search_result.id, user)
        
        expect(response).to have_http_status(:ok)
        
        citation_data = JSON.parse(response.body)
        expect(citation_data['id']).to eq(web_search_result.id)
        expect(citation_data['message_id']).to eq(message.id)
        expect(citation_data['url']).to eq('https://example.com/article')
        expect(citation_data['title']).to eq('Example Article')
        expect(citation_data['content']).to eq('This is an example article content for testing citations.')
        expect(citation_data['start_index']).to eq(0)
        expect(citation_data['end_index']).to eq(50)
        expect(citation_data['created_at']).to be_present
      end

      it 'returns correct JSON structure' do
        get_citation(web_search_result.id, user)
        
        expect(response).to have_http_status(:ok)
        
        citation_data = JSON.parse(response.body)
        expect(citation_data.keys).to contain_exactly(
          'id', 'message_id', 'url', 'title', 'content', 
          'start_index', 'end_index', 'created_at'
        )
      end
    end

    context 'when citation does not exist' do
      it 'returns not found error' do
        get_citation(99999, user)
        
        expect(response).to have_http_status(:not_found)
        
        error_data = JSON.parse(response.body)
        expect(error_data['error']).to eq('Citation not found')
      end
    end

    context 'when user does not own the chat' do
      let!(:other_user) { create(:user) }
      let!(:other_organization) { create(:organization) }
      let!(:other_membership) { create(:membership, user: other_user, organization: other_organization) }

      it 'returns unauthorized error' do
        get_citation(web_search_result.id, other_user)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get "/v1/citations/#{web_search_result.id}", {}
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with different web search result data' do
      let!(:detailed_web_search_result) do
        create(:web_search_result,
               message: message,
               url: 'https://detailed-example.com/research',
               title: 'Detailed Research Article',
               content: 'This is a comprehensive research article with detailed information about the topic.',
               start_index: 25,
               end_index: 125,
               image: 'https://example.com/image.jpg',
               site_name: 'Research Site')
      end

      it 'returns detailed citation data' do
        get_citation(detailed_web_search_result.id, user)
        
        expect(response).to have_http_status(:ok)
        
        citation_data = JSON.parse(response.body)
        expect(citation_data['url']).to eq('https://detailed-example.com/research')
        expect(citation_data['title']).to eq('Detailed Research Article')
        expect(citation_data['content']).to include('comprehensive research article')
        expect(citation_data['start_index']).to eq(25)
        expect(citation_data['end_index']).to eq(125)
      end
    end

    context 'when web search result has minimal data' do
      let!(:minimal_web_search_result) do
        create(:web_search_result,
               message: message,
               url: 'https://minimal.com',
               title: nil,
               content: nil,
               start_index: nil,
               end_index: nil)
      end

      it 'returns citation with nil values' do
        get_citation(minimal_web_search_result.id, user)
        
        expect(response).to have_http_status(:ok)
        
        citation_data = JSON.parse(response.body)
        expect(citation_data['url']).to eq('https://minimal.com')
        expect(citation_data['title']).to be_nil
        expect(citation_data['content']).to be_nil
        expect(citation_data['start_index']).to be_nil
        expect(citation_data['end_index']).to be_nil
      end
    end

    context 'when web search result is discarded' do
      before do
        web_search_result.update!(discarded_at: Time.current)
      end

      it 'returns not found error' do
        get_citation(web_search_result.id, user)
        
        expect(response).to have_http_status(:not_found)
        
        error_data = JSON.parse(response.body)
        expect(error_data['error']).to eq('Citation not found')
      end
    end
  end
end
