# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'POST /v1/organizations/:organization_id/transfer_ownership', type: :request do
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:owner) { create(:user) }
  let!(:admin) { create(:user) }
  let!(:partner_admin) { create(:user) }
  let!(:new_owner) { create(:user) }
  let!(:regular_member) { create(:user) }
  let!(:non_member) { create(:user) }

  let!(:owner_membership) { create(:membership, :owner, user: owner, organization: organization) }
  let!(:admin_membership) { create(:membership, :admin, user: admin, organization: organization) }
  let!(:partner_admin_membership) do
    create(:membership, user: partner_admin, organization: organization, role: Membership.role_mappings['partner_admin'])
  end
  let!(:member_membership) { create(:membership, user: regular_member, organization: organization) }
  let!(:new_owner_membership) { create(:membership, user: new_owner, organization: organization) }

  let(:valid_params) do
    {
      designated_owner_email: new_owner.email
    }
  end

  def transfer_ownership(organization_id, params, which_user = owner)
    post "/v1/organizations/#{organization_id}/transfer_ownership", params, as_user(which_user)
  end

  describe 'POST /v1/organizations/:organization_id/transfer_ownership' do
    context 'when current owner transfers ownership' do
      it 'successfully transfers ownership to existing member' do
        transfer_ownership(organization.id, valid_params, owner)
        
        expect_response(:ok)
        
        # Check that new owner has owner role
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['owner'])
        
        # Check that old owner has admin role
        owner_membership.reload
        expect(owner_membership.role).to eq(Membership.role_mappings['admin'])
      end

      it 'transfers ownership to user not yet in organization' do
        non_member_params = { designated_owner_email: non_member.email }
        
        transfer_ownership(organization.id, non_member_params, owner)
        
        expect_response(:ok)
        
        # Check that new membership was created for non-member
        new_membership = Membership.find_by(user: non_member, organization: organization)
        expect(new_membership).to be_present
        expect(new_membership.role).to eq(Membership.role_mappings['owner'])
        
        # Check that old owner has admin role
        owner_membership.reload
        expect(owner_membership.role).to eq(Membership.role_mappings['admin'])
      end

      it 'handles case insensitive email' do
        uppercase_params = { designated_owner_email: new_owner.email.upcase }
        
        transfer_ownership(organization.id, uppercase_params, owner)
        
        expect_response(:ok)
        
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['owner'])
      end

      it 'handles email with whitespace' do
        whitespace_params = { designated_owner_email: "  #{new_owner.email}  " }
        
        transfer_ownership(organization.id, whitespace_params, owner)
        
        expect_response(:ok)
        
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['owner'])
      end
    end

    context 'when admin transfers ownership' do
      it 'allows admin to transfer ownership' do
        transfer_ownership(organization.id, valid_params, admin)
        
        expect_response(:ok)
        
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['owner'])
      end
    end

    context 'when partner admin transfers ownership' do
      it 'allows partner admin to transfer ownership' do
        transfer_ownership(organization.id, valid_params, partner_admin)
        
        expect_response(:ok)
        
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['owner'])
      end
    end

    context 'when regular member tries to transfer ownership' do
      it 'denies access to regular members' do
        transfer_ownership(organization.id, valid_params, regular_member)
        
        expect_error_response(:unauthorized)
        
        # Verify ownership hasn't changed
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['member'])
      end
    end

    context 'when owner tries to transfer ownership of different organization' do
      it 'denies access to organizations not owned' do
        transfer_ownership(other_organization.id, valid_params, owner)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with invalid parameters' do
      it 'returns error for missing email' do
        transfer_ownership(organization.id, {}, owner)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error for empty email' do
        transfer_ownership(organization.id, { designated_owner_email: '' }, owner)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error for invalid email format' do
        transfer_ownership(organization.id, { designated_owner_email: 'invalid-email' }, owner)
        
        expect_error_response(:unprocessable_entity)
      end

      it 'returns error for non-existent user email' do
        transfer_ownership(organization.id, { designated_owner_email: '<EMAIL>' }, owner)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when transferring to current owner' do
      it 'returns error when trying to transfer to self' do
        self_params = { designated_owner_email: owner.email }
        
        transfer_ownership(organization.id, self_params, owner)
        
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when organization does not exist' do
      it 'returns not found error' do
        transfer_ownership(99999, valid_params, owner)
        
        expect_error_response(:not_found)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        post "/v1/organizations/#{organization.id}/transfer_ownership", valid_params
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when transferring to user with existing higher role' do
      before do
        # Make new_owner an admin first
        new_owner_membership.update!(role: Membership.role_mappings['admin'])
      end

      it 'successfully promotes admin to owner' do
        transfer_ownership(organization.id, valid_params, owner)
        
        expect_response(:ok)
        
        new_owner_membership.reload
        expect(new_owner_membership.role).to eq(Membership.role_mappings['owner'])
      end
    end
  end
end
