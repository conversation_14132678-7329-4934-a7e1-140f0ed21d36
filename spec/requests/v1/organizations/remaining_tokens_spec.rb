# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GET /v1/organizations/:organization_id/remaining_tokens', type: :request do
  let!(:organization) { create(:organization) }
  let!(:other_organization) { create(:organization) }
  let!(:admin) { create(:user) }
  let!(:partner_admin) { create(:user) }
  let!(:owner) { create(:user) }
  let!(:regular_member) { create(:user) }
  let!(:other_org_admin) { create(:user) }

  let!(:admin_membership) { create(:membership, :admin, user: admin, organization: organization) }
  let!(:partner_admin_membership) do
    create(:membership, user: partner_admin, organization: organization, role: Membership.role_mappings['partner_admin'])
  end
  let!(:owner_membership) { create(:membership, :owner, user: owner, organization: organization) }
  let!(:member_membership) { create(:membership, user: regular_member, organization: organization) }
  let!(:other_admin_membership) { create(:membership, :admin, user: other_org_admin, organization: other_organization) }

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      purchased_credits: 1000.0,
      remaining_monthly_credits: 750.0,
      monthly_credits_refresh: 500,
      refresh_date: 15
    )
  end

  let!(:other_org_plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: other_organization.id,
      purchased_credits: 2000.0,
      remaining_monthly_credits: 1500.0,
      monthly_credits_refresh: 1000,
      refresh_date: 20
    )
  end

  def get_remaining_tokens(organization_id, which_user = admin)
    get "/v1/organizations/#{organization_id}/remaining_tokens", {}, as_user(which_user)
  end

  describe 'GET /v1/organizations/:organization_id/remaining_tokens' do
    context 'when user is admin' do
      it 'returns organization token information' do
        get_remaining_tokens(organization.id, admin)
        
        expect_response(:ok)
        
        expect(response_data['id']).to eq(organization.id)
        expect(response_data['name']).to eq(organization.name)
        expect(response_data['purchased_credits']).to eq(1000.0)
        expect(response_data['remaining_monthly_credits']).to eq(750.0)
        expect(response_data['monthly_credits_refresh']).to eq(500)
        expect(response_data['refresh_date']).to eq(15)
      end

      it 'returns correct JSON structure' do
        get_remaining_tokens(organization.id, admin)
        
        expect_response(:ok)
        
        expect(response_data.keys).to contain_exactly(
          'id', 'name', 'purchased_credits', 'remaining_monthly_credits',
          'monthly_credits_refresh', 'refresh_date'
        )
      end
    end

    context 'when user is partner admin' do
      before do
        # Set up partner admin to own the organization
        organization.update!(created_by_id: partner_admin.id)
        partner_admin_membership.update!(managed_organization_ids: [organization.id])
      end

      it 'allows partner admin to access owned organization tokens' do
        get_remaining_tokens(organization.id, partner_admin)
        
        expect_response(:ok)
        
        expect(response_data['id']).to eq(organization.id)
        expect(response_data['purchased_credits']).to eq(1000.0)
      end

      it 'denies access to non-owned organizations' do
        get_remaining_tokens(other_organization.id, partner_admin)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when user is owner' do
      it 'allows owner to access organization tokens' do
        get_remaining_tokens(organization.id, owner)
        
        expect_response(:ok)
        
        expect(response_data['purchased_credits']).to eq(1000.0)
      end
    end

    context 'when user is regular member' do
      it 'denies access to regular members' do
        get_remaining_tokens(organization.id, regular_member)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when user tries to access different organization' do
      it 'denies access to organizations user is not admin of' do
        get_remaining_tokens(other_organization.id, admin)
        
        expect_error_response(:unauthorized)
      end
    end

    context 'when organization has no plan threshold' do
      before do
        OrganizationsPlansThreshold.where(organization_id: organization.id).destroy_all
      end

      it 'returns organization info with nil values for plan data' do
        get_remaining_tokens(organization.id, admin)
        
        expect_response(:ok)
        
        expect(response_data['id']).to eq(organization.id)
        expect(response_data['name']).to eq(organization.name)
        expect(response_data['purchased_credits']).to eq(0.0)
        expect(response_data['remaining_monthly_credits']).to eq(0.0)
        expect(response_data['monthly_credits_refresh']).to be_nil
        expect(response_data['refresh_date']).to be_nil
      end
    end

    context 'when organization does not exist' do
      it 'returns not found error' do
        get_remaining_tokens(99999, admin)
        
        expect_error_response(:not_found)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get "/v1/organizations/#{organization.id}/remaining_tokens", {}
        
        expect_error_response(:unauthorized)
      end
    end

    context 'with different credit scenarios' do
      context 'when organization has zero credits' do
        before do
          plan_threshold.update!(
            purchased_credits: 0.0,
            remaining_monthly_credits: 0.0
          )
        end

        it 'returns zero credit values' do
          get_remaining_tokens(organization.id, admin)
          
          expect_response(:ok)
          
          expect(response_data['purchased_credits']).to eq(0.0)
          expect(response_data['remaining_monthly_credits']).to eq(0.0)
        end
      end

      context 'when organization has high credit values' do
        before do
          plan_threshold.update!(
            purchased_credits: 999999.99,
            remaining_monthly_credits: 888888.88
          )
        end

        it 'returns high credit values correctly' do
          get_remaining_tokens(organization.id, admin)
          
          expect_response(:ok)
          
          expect(response_data['purchased_credits']).to eq(999999.99)
          expect(response_data['remaining_monthly_credits']).to eq(888888.88)
        end
      end

      context 'when organization has negative credits' do
        before do
          plan_threshold.update!(
            purchased_credits: -100.0,
            remaining_monthly_credits: -50.0
          )
        end

        it 'returns negative credit values' do
          get_remaining_tokens(organization.id, admin)
          
          expect_response(:ok)
          
          expect(response_data['purchased_credits']).to eq(-100.0)
          expect(response_data['remaining_monthly_credits']).to eq(-50.0)
        end
      end
    end

    context 'when partner admin has multiple managed organizations' do
      let!(:managed_org_1) { create(:organization, created_by_id: partner_admin.id) }
      let!(:managed_org_2) { create(:organization, created_by_id: partner_admin.id) }
      
      before do
        partner_admin_membership.update!(managed_organization_ids: [managed_org_1.id, managed_org_2.id])
        
        OrganizationsPlansThreshold.create!(
          organization_id: managed_org_1.id,
          purchased_credits: 500.0,
          remaining_monthly_credits: 300.0
        )
      end

      it 'allows access to all managed organizations' do
        get_remaining_tokens(managed_org_1.id, partner_admin)
        
        expect_response(:ok)
        expect(response_data['purchased_credits']).to eq(500.0)
      end

      it 'denies access to non-managed organizations' do
        get_remaining_tokens(organization.id, partner_admin)
        
        expect_error_response(:unauthorized)
      end
    end
  end
end
