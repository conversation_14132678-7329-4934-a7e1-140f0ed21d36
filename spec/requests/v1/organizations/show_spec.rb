# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Organizations#show', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'ORG Main', code: 'org-main')
  end

  let!(:organization2) do
    Organization.create!(name: 'ORG Main 2', code: 'org-main-2')
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: -3
    )
  end

  let!(:membership_partner_admin) do
    Membership.create!(
      user_id: partner_admin.id,
      organization_id: organization.id,
      role: -1
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization2.id,
      role: 0
    )
  end

  def show_organization(id, user)
    get "/v1/organizations/#{id}", {}, as_user(user)
  end

  describe 'GET show organization' do
    it 'return error when organization not found' do
      show_organization(-10, admin)
      expect_response(:not_found)
    end

    it 'return error when user is not admin' do
      show_organization(organization.id, member)
      expect_response(:forbidden)
    end

    context 'when user is admin' do
      it 'return organization' do
        show_organization(organization.id, admin)
        expect_response(:ok)

        expect(response_data['id']).to eq organization.id
        expect(response_data['name']).to eq organization.name
        expect(response_data['code']).to eq organization.code
      end

      it 'return other organization' do
        show_organization(organization2.id, admin)
        expect_response(:ok)

        expect(response_data['id']).to eq organization2.id
        expect(response_data['name']).to eq organization2.name
        expect(response_data['code']).to eq organization2.code
      end
    end

    context 'when user is partner admin' do
      it 'return organization main' do
        show_organization(organization.id, partner_admin)
        expect_response(:ok)

        expect(response_data['id']).to eq organization.id
        expect(response_data['name']).to eq organization.name
        expect(response_data['code']).to eq organization.code
      end

      it 'return organization created by user' do
        organization2.update!(created_by_id: partner_admin.id)
        show_organization(organization2.id, partner_admin)
        expect_response(:ok)

        expect(response_data['id']).to eq organization2.id
        expect(response_data['name']).to eq organization2.name
        expect(response_data['code']).to eq organization2.code
      end

      it 'return error when organization is not created by user' do
        show_organization(organization2.id, partner_admin)
        expect_response(:forbidden)
      end
    end

    context 'when user is member' do
      it 'return error when organization is not member' do
        show_organization(organization.id, member)
        expect_response(:forbidden)
      end

      it 'return organization' do
        show_organization(organization2.id, member)
        expect_response(:ok)

        expect(response_data['id']).to eq organization2.id
        expect(response_data['name']).to eq organization2.name
        expect(response_data['code']).to eq organization2.code
      end
    end
  end
end
