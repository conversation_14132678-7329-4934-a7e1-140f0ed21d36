# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Organizations#destroy', type: :request do
  let!(:admin) { create(:user) }
  let!(:partner_admin) { create(:user) }
  let!(:super_admin) { create(:user) }
  let!(:super_admin_platform) { create(:user) }
  let!(:owner_platform) { create(:user) }
  let!(:member) { create(:user) }
  let!(:organization) { Organization.first }
  let!(:organization2) { create(:organization) }
  let!(:organization3) { create(:organization, created_by_id: partner_admin.id) }

  let!(:membership_admin) { create(:membership, :admin, user: admin, organization:) }

  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: organization2)
  end

  let!(:membership_super_admin) { create(:membership, :super_admin, user: super_admin, organization:) }
  let!(:membership_super_admin_platform) do
    create(:membership, :super_admin_platform, user: super_admin_platform, organization:)
  end

  let!(:membership_owner_platform) { create(:membership, :owner_platform, user: owner_platform, organization:) }
  let!(:membership_member) { create(:membership, :member, user: member, organization: organization2) }

  def destroy_organization(id, user)
    delete "/v1/organizations/#{id}", {}, as_user(user)
  end

  describe 'DELETE destroy organization' do
    it 'return error when organization not found' do
      destroy_organization(-10, admin)
      expect_response(:not_found)
    end

    it 'return error when user is not authorized' do
      destroy_organization(organization.id, member)
      expect_response(:forbidden)
    end

    context 'when user is admin' do
      it 'soft deletes organization successfully' do
        destroy_organization(organization.id, admin)
        expect_response(:ok)

        organization.reload
        expect(organization.discarded_at).not_to be_nil
      end

      it 'soft deletes organization when partner admin member of organization ' do
        destroy_organization(organization2.id, partner_admin)
        expect_response(:ok)
      end
      it 'return error when organization is not owned' do
        destroy_organization(organization.id, partner_admin)
        expect_response(:forbidden)
      end

      it 'can delete organization owned by partner admin' do
        destroy_organization(organization3.id, partner_admin)
        expect_response(:ok)

        organization3.reload
        expect(organization3.discarded_at).not_to be_nil
      end
    end

    context 'when user is super_admin_platform' do
      it 'soft deletes organization successfully' do
        destroy_organization(organization.id, super_admin_platform)
        expect_response(:ok)

        organization.reload
        expect(organization.discarded_at).not_to be_nil
      end

      it 'can delete any organization' do
        destroy_organization(organization2.id, super_admin_platform)
        expect_response(:ok)

        organization2.reload
        expect(organization2.discarded_at).not_to be_nil
      end
    end

    context 'when user is owner_platform' do
      it 'soft deletes organization successfully' do
        destroy_organization(organization.id, owner_platform)
        expect_response(:ok)

        organization.reload
        expect(organization.discarded_at).not_to be_nil
      end

      it 'can delete any organization' do
        destroy_organization(organization2.id, owner_platform)
        expect_response(:ok)

        organization2.reload
        expect(organization2.discarded_at).not_to be_nil
      end
    end

    context 'when user is member' do
      it 'return error when trying to delete organization' do
        destroy_organization(organization2.id, member)
        expect_response(:forbidden)
      end
    end
  end
end
