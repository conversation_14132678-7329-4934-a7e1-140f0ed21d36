require 'rails_helper'

RSpec.describe 'Api::V1::Chats#index', type: :request do
  let!(:test_user) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member2) do
    User.create!(
      display_name: 'Member2',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) { create(:organization, name: 'Test') }

  let!(:workspace) do
    Workspace.create!(
      organization_id: organization.id,
      name: 'Test'
    )
  end

  let!(:membership_test_user) do
    Membership.create!(
      user_id: test_user.id,
      organization_id: organization.id
    )
  end

  let!(:workspace_membership) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user.id,
      workspace_id: workspace.id
    )
  end

  let!(:organization2) do
    Organization.create!(name: 'Test2')
  end

  let!(:workspace2) do
    Workspace.create!(
      organization_id: organization2.id,
      name: 'Test2'
    )
  end

  let!(:model) do
    Model.create!(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098'
    )
  end

  def index_chat(workspace_id, params, which_user = user)
    get "/v1/workspaces/#{workspace_id}/chats", params, as_user(which_user)
  end

  def create_chat(params, which_user = user)
    post '/v1/chats', params, as_user(which_user)
  end

  before do
    # stub openai method
    allow_any_instance_of(OpenaiService).to receive(:create_assistant)
                                        .with(anything)
      .and_return({ 'id' => 'asst_xyz123' })
  end

  context 'with valid query' do
    let!(:model_template) do
      ModelTemplate.create!(
        name: 'Test Template',
        description: 'Test Desc',
        max_tokens: 100,
        temperature: 1.0,
        model: 'openai/gpt-4o',
        instruction: 'Chat GPT',
        prompt: 'test',
        placeholder: 'reference',
        organization_id: organization.id,
        user: test_user
      )
    end

    let!(:model_from_template) do
      Model.create!(
        name: 'Test Template',
        max_tokens: 100,
        temperature: 1.0,
        model: 'openai/gpt-4o',
        instruction: 'Chat GPT',
        openai_assistant_id: 'asst_mba231',
        model_template_id: model_template.id
      )
    end

    before do
      params = {
        name: 'new chat gpt4o',
        model: 'openai/gpt-4o',
        chat_type: 'general'
      }

      create_chat(params, test_user)

      params[:name] = 'new chat template'
      params[:source_model_template_id] = model_template.id

      create_chat(params, test_user)

      Chat.create!(
        name: 'org2 chat',
        model: model,
        workspace_id: workspace2.id,
        chat_type: 'general'
      )
    end

    it 'return list owned chats by workspace ' do
      index_chat(workspace.id, {}, test_user)
      expect_response(:ok)

      expect(response_data.size).to eq 2
    end

    it 'return list owned chats by workspace2' do
      index_chat(workspace2.id, {}, member2)
      expect_response(:ok)

      expect(response_data.size).to eq 0
    end

    it "return filtered list owned chats with 'search' params" do
      index_chat(workspace.id, { search: 'gpt4o' }, test_user)
      expect_response(:ok)

      expect(response_data.size).to eq 1

      expect(response_data.first['name']).to eq 'new chat gpt4o'
    end

    it "return filtered list owned chats with 'test_prompt' params" do
      chat = Chat.find_by(workspace_id: workspace.id)
      model_template.update!(test_prompt_chat_id: chat.id)

      index_chat(workspace.id, { test_prompt: true }, test_user)
      expect_response(:ok)
      expect(response_data.size).to eq 1

      expect(response_data.first['name']).to eq chat.name
    end
  end
end
