# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::UserManagements#change_password_request', type: :request do
  let!(:user) do
    create(:user,
           display_name: 'Test User',
           email: '<EMAIL>',
           password: 'password123')
  end

  let!(:organization) do
    Organization.first
  end

  let!(:membership) do
    create(:membership,
           user_id: user.id,
           organization_id: organization.id,
           role: Membership.role_mappings['member'])
  end

  let(:valid_params) do
    {
      email: '<EMAIL>'
    }
  end

  def change_password_request(params)
    post '/v1/user_managements/change_password_request', params
  end

  describe 'POST /v1/user_managements/change_password_request' do
    context 'when request is invalid' do
      it 'returns 422 with error message when email is missing' do
        change_password_request({})
        expect_response(:unprocessable_entity)
      end

      it 'returns 422 with error message when email is empty' do
        change_password_request({ email: '' })
        expect_response(:unprocessable_entity)
      end
    end

    context 'when user exists and has organization membership' do
      it 'creates a token request successfully' do
        change_password_request(valid_params)
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
        expect(token_request.request_status).to eq('requested')
        expect(token_request.purpose).to eq('change_password')
        expect(token_request.user_id.to_s).to eq(user.id.to_s)
        expect(token_request.request_expiry_date).to be_within(1.second).of(Time.current + 10.minutes)
        expect(token_request.requested_at).to be_within(1.second).of(Time.current)
        expect(token_request.request_code).to be_present
        expect(token_request.request_code.length).to eq(128) # 64 hex characters = 128 chars
      end

      it 'returns correct response format' do
        change_password_request(valid_params)
        expect_response(:created)

        expect(response_data['email']).to eq('<EMAIL>')
        expect(response_data['request_status']).to eq('requested')
        expect(response_data['request_expiry_date']).to be_present
      end

      it 'enqueues password change request mailer job' do
        expect(Mailer::PasswordChangeRequestMailerJob).to receive(:perform_later)

        change_password_request(valid_params)
        expect_response(:created)
      end

      it 'downcases the email' do
        change_password_request({ email: '<EMAIL>' })
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
      end
    end

    context 'when user does not exist' do
      let(:invalid_params) { { email: '<EMAIL>' } }

      it 'returns 422 with invalid email message' do
        change_password_request(invalid_params)
        expect_response(:unprocessable_entity)

        expect_error_response(422, 'Email Invalid')
      end
    end

    context 'when user has no organization membership' do
      before do
        membership.discard
      end

      it 'returns 422 with organization error message' do
        change_password_request(valid_params)
        expect_response(:unprocessable_entity)

        expect_error_response(422, 'You are not in any organization')
      end
    end

    context 'when organization is discarded' do
      before do
        membership.undiscard
        organization.discard!
      end

      it 'returns 422 with organization deleted message' do
        change_password_request(valid_params)
        expect_response(:unprocessable_entity)

        expect_error_response(422, 'You are not in any organization')
      end
    end

    context 'when user has multiple memberships' do
      let!(:organization2) { create(:organization, name: 'Second Organization') }
      let!(:membership2) do
        create(:membership,
               user_id: user.id,
               organization_id: organization2.id,
               role: Membership.role_mappings['member'])
      end

      it 'uses the first membership organization' do
        change_password_request(valid_params)
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.user_id.to_s).to eq(user.id.to_s)
      end
    end

    context 'when there is an existing unexpired request' do
      let!(:existing_request) do
        create(:token_request,
               email: '<EMAIL>',
               request_status: 'requested',
               request_code: SecureRandom.hex(64),
               request_expiry_date: Time.current + 5.minutes,
               requested_at: Time.current,
               user_id: user.id,
               purpose: 'change_password')
      end

      it 'returns 422 with already requested message' do
        change_password_request(valid_params)
        expect_response(:unprocessable_entity)

        expect_error_response(422, 'Already requested change password')
      end
    end

    context 'when there is an expired request' do
      let!(:expired_request) do
        create(:token_request,
               email: '<EMAIL>',
               request_status: 'requested',
               request_code: SecureRandom.hex(64),
               request_expiry_date: Time.current - 1.minute,
               requested_at: Time.current - 11.minutes,
               user_id: user.id,
               purpose: 'change_password')
      end

      it 'allows creating a new request' do
        change_password_request(valid_params)
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
        expect(token_request.request_status).to eq('requested')
      end
    end

    context 'when there is a confirmed request' do
      let!(:confirmed_request) do
        create(:token_request,
               email: '<EMAIL>',
               request_status: 'confirmed',
               request_code: SecureRandom.hex(64),
               request_expiry_date: Time.current + 5.minutes,
               requested_at: Time.current,
               user_id: user.id,
               purpose: 'change_password')
      end

      it 'allows creating a new request' do
        change_password_request(valid_params)
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
        expect(token_request.request_status).to eq('requested')
      end
    end

    context 'when request code collision occurs' do
      before do
        allow(SecureRandom).to receive(:hex).and_return('collision_code')

        # Create a request with the collision code
        create(:token_request,
               email: '<EMAIL>',
               request_status: 'requested',
               request_code: 'collision_code',
               request_expiry_date: Time.current + 5.minutes,
               requested_at: Time.current,
               user_id: create(:user).id,
               purpose: 'change_password')
      end

      it 'returns 422 with request failed message' do
        change_password_request(valid_params)
        expect_response(:unprocessable_entity)

        expect_error_response(422, 'Request failed, please try again')
      end
    end

    context 'when user is platform admin' do
      before do
        membership.update!(role: Membership.role_mappings['super_admin_platform'])
      end

      it 'creates token request successfully' do
        change_password_request(valid_params)
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
        expect(token_request.request_status).to eq('requested')
      end
    end

    context 'when user is partner admin' do
      before do
        membership.update!(role: Membership.role_mappings['partner_admin'])
      end

      it 'creates token request successfully' do
        change_password_request(valid_params)
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
        expect(token_request.request_status).to eq('requested')
      end
    end

    context 'with different email formats' do
      it 'handles mixed case email' do
        change_password_request({ email: '<EMAIL>' })
        expect_response(:created)

        token_request = TokenRequest.last
        expect(token_request.email).to eq('<EMAIL>')
      end
    end

    context 'when multiple requests are made in quick succession' do
      it 'prevents duplicate requests within expiry window' do
        # First request should succeed
        change_password_request(valid_params)
        expect_response(:created)

        # Second request should fail
        change_password_request(valid_params)
        expect_response(:unprocessable_entity)

        expect_error_response(422, 'Already requested change password')
      end
    end
  end
end
