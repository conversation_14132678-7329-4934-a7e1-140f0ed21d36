# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::UserManagements#create_invitation', type: :request do
  let!(:admin) do
    User.create(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: 'password123'
    )
  end

  let!(:organization) { create(:organization, name: 'Main Organization') }

  let!(:other_organization) { create(:organization, name: 'Other Organization') }

  let!(:membership) do
    Membership.create(
      user_id: admin.id,
      organization_id: organization.id,
      role: -3
    )
  end

  let!(:valid_params) do
    {
      email: '<EMAIL>',
      role: 'admin',
      organization_team_id: nil,
      organization_id: organization.id
    }
  end

  def create_invitation(params, user)
    post '/v1/user_managements/invite', params, as_user(user)
  end

  describe 'POST /v1/user_managements/invite' do
    context 'when request invalid' do
      it 'returns 422 with error message' do
        create_invitation({}, admin)
        expect_response(:unprocessable_entity)
      end
    end

    context 'when accessed by unauthorized user' do
      it 'returns forbidden when user is not admin of the organization' do
        membership.update!(role: 0)
        membership.update!(organization_id: other_organization.id)
        create_invitation(valid_params, admin)
        expect_response(:forbidden)
      end

      it 'returns forbidden when user is a member but not an admin' do
        membership.update!(role: 3)
        create_invitation(valid_params, admin)
        expect_response(:forbidden)
      end
    end

    context 'when user exist' do
      let!(:existing_user) do
        User.create(
          display_name: 'Existing User',
          email: valid_params[:email],
          password: 'password123'
        )
      end

      let(:exist_params) do
        valid_params.merge(email: existing_user.email)
      end

      it 'returns create membership' do
        create_invitation(exist_params, admin)
        expect_response(:created)

        membership = Membership.find_by(user_id: existing_user.id, organization_id: organization.id)
        expect(membership).to be_present
        expect(membership.role).to eq(Membership.role_mappings[valid_params[:role]])
      end

      it 'not call mailer job' do
        expect(Mailer::UserInvitationMailerJob).not_to receive(:perform_later)
        expect(Mailer::InviteMemberNotificationMailerJob).not_to receive(:perform_later)

        create_invitation(exist_params, admin)
        expect_response(:created)
      end

      it 'return error when already ini membership' do
        Membership.create!(
          user_id: existing_user.id,
          organization_id: organization.id,
          role: Membership.role_mappings['member']
        )

        create_invitation(exist_params, admin)
        expect_response(:forbidden)
      end
    end

    it 'creates an invitation successfully' do
      create_invitation(valid_params, admin)
      expect_response(:created)

      user_invite = UserInvitation.last
      expect(user_invite.email).to eq(valid_params[:email])
      expect(user_invite.invitation_status).to eq('invited')
      expect(user_invite.organization_id).to eq(valid_params[:organization_id])
    end

    it 'send invitation email' do
      expect(Mailer::UserInvitationMailerJob).to receive(:perform_later).and_call_original
      expect(Mailer::InviteMemberNotificationMailerJob).to receive(:perform_later).and_call_original

      create_invitation(valid_params, admin)
      expect_response(:created)
    end
  end
end
