# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::UserManagements#list_invited_users', type: :request do
  let!(:admin) { create(:user) }
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:organization2) { create(:organization) }
  let!(:organization3) { create(:organization) }
  let!(:membership_admin) { create(:membership, :admin, user: admin, organization:) }
  let!(:membership_admin2) { create(:membership, :admin, user: admin, organization: organization2) }
  let!(:membership_user) { create(:membership, user:, organization:) }

  let!(:user_invitation) do
    create(:user_invitation, email: '<EMAIL>', organization:, invitation_status: 'invited')
  end
  let!(:user_invitation2) do
    create(:user_invitation, email: '<EMAIL>', organization: organization2, invitation_status: 'invited')
  end
  let!(:user_invitation3) do
    create(:user_invitation, email: '<EMAIL>', organization: organization2, invitation_status: 'invited')
  end

  def list_invited_users(params, user)
    get '/v1/user_managements/invited_users', params, as_user(user)
  end

  describe 'GET list invited users' do
    it 'return list invited users for admin' do
      list_invited_users({}, admin)
      expect_response(:ok)

      expect(response_data.size).to eq 1
    end

    it 'return list filtered by organization_id for admin' do
      list_invited_users({ organization_id: organization2.id }, admin)
      expect_response(:ok)

      expect(response_data.size).to eq 2
    end

    it 'return list invited users for user' do
      list_invited_users({}, user)
      expect_error_response(:forbidden)
    end
  end
end
