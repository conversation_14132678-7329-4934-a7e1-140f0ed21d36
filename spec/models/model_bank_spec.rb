# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ModelBank, type: :model do
  describe 'validations' do
    it 'is valid with valid attributes' do
      model_bank = ModelBank.new(
        name: 'Test Model Bank',
        code: 'TEST001',
        input_rate: 0.001,
        output_rate: 0.002,
        web_search_rate: 0.005,
        image_rate: 0.01,
        file_rate: 0.003,
        status: 'active'
      )

      expect(model_bank).to be_valid
    end
  end

  describe 'associations' do
    it 'has many prompt_eval_results' do
      model_bank = create(:model_bank)
      create_list(:prompt_eval_result, 3, model_bank:)
      expect(model_bank.prompt_eval_results.count).to eq(3)
    end
  end

  describe 'scopes' do
    let!(:active_bank) { create(:model_bank, status: 'active') }
    let!(:inactive_bank) { create(:model_bank, status: 'inactive') }

    describe '.active' do
      it 'returns only active model banks' do
        expect(ModelBank.active).to include(active_bank)
        expect(ModelBank.active).not_to include(inactive_bank)
      end
    end
  end

  describe 'enums' do
    it 'defines status enum' do
      expect(ModelBank.statuses).to include('active', 'inactive')
    end
  end

  describe '#total_rate' do
    it 'returns the sum of all rates' do
      model_bank = create(:model_bank,
                          input_rate: 0.001,
                          output_rate: 0.002,
                          web_search_rate: 0.005,
                          image_rate: 0.01,
                          file_rate: 0.003)

      expect(model_bank.total_rate).to eq(0.021)
    end
  end

  describe 'status methods' do
    let(:active_bank) { create(:model_bank, status: 'active') }
    let(:inactive_bank) { create(:model_bank, status: 'inactive') }

    describe '#active?' do
      it 'returns true for active banks' do
        expect(active_bank.active?).to be true
        expect(inactive_bank.active?).to be false
      end
    end

    describe '#inactive?' do
      it 'returns true for inactive banks' do
        expect(inactive_bank.inactive?).to be true
        expect(active_bank.inactive?).to be false
      end
    end
  end
end
