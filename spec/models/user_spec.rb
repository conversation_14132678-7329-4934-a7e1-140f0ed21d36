# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User, type: :model do
  it 'should have a valid factory' do
    expect(build(:user)).to be_valid
  end

  it 'should not be valid without a display name' do
    expect(build(:user, display_name: nil)).not_to be_valid
  end

  it 'should not be valid without an email' do
    expect(build(:user, email: nil)).not_to be_valid
  end

  it 'should not be valid without a password' do
    expect(build(:user, password: nil)).not_to be_valid
  end

  it 'should not be valid with a duplicate email' do
    create(:user, email: '<EMAIL>')
    expect(build(:user, email: '<EMAIL>')).not_to be_valid
  end
end
