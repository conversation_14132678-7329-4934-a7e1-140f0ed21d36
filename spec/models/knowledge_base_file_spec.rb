# frozen_string_literal: true

require 'rails_helper'

RSpec.describe KnowledgeBaseFile, type: :model do
  let!(:organization) { create(:organization) }

  describe 'validations' do
    it 'is valid with valid attributes' do
      knowledge_base_file = KnowledgeBaseFile.new(
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf',
        content_type: 'application/pdf',
        file_size: 1024,
        organization: organization
      )

      expect(knowledge_base_file).to be_valid
    end

    it 'requires a name' do
      knowledge_base_file = KnowledgeBaseFile.new(
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf',
        organization: organization
      )

      expect(knowledge_base_file).not_to be_valid
      expect(knowledge_base_file.errors[:name]).to include("can't be blank")
    end

    it 'requires a filename' do
      knowledge_base_file = KnowledgeBaseFile.new(
        name: 'Test File',
        file_url: 'https://example.com/test.pdf',
        organization: organization
      )

      expect(knowledge_base_file).not_to be_valid
      expect(knowledge_base_file.errors[:filename]).to include("can't be blank")
    end

    it 'requires an organization' do
      knowledge_base_file = KnowledgeBaseFile.new(
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf'
      )

      expect(knowledge_base_file).not_to be_valid
      expect(knowledge_base_file.errors[:organization]).to include('must exist')
    end
  end

  describe 'associations' do
    it 'belongs to an organization' do
      knowledge_base_file = KnowledgeBaseFile.create(
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf',
        organization: organization
      )

      expect(knowledge_base_file.organization).to eq(organization)
    end
  end

  describe 'scopes' do
    let!(:active_file) do
      KnowledgeBaseFile.create(
        name: 'Active File',
        filename: 'active.pdf',
        file_url: 'https://example.com/active.pdf',
        is_active: true,
        organization: organization
      )
    end

    let!(:inactive_file) do
      KnowledgeBaseFile.create(
        name: 'Inactive File',
        filename: 'inactive.pdf',
        file_url: 'https://example.com/inactive.pdf',
        is_active: false,
        organization: organization
      )
    end

    let!(:other_org_file) do
      other_org = Organization.create(name: 'Other Organization')
      KnowledgeBaseFile.create(
        name: 'Other Org File',
        filename: 'other.pdf',
        file_url: 'https://example.com/other.pdf',
        organization: other_org
      )
    end

    describe '.active' do
      it 'returns only active files' do
        expect(KnowledgeBaseFile.active).to include(active_file)
        expect(KnowledgeBaseFile.active).not_to include(inactive_file)
      end
    end

    describe '.by_organization' do
      it 'returns files for the specified organization' do
        expect(KnowledgeBaseFile.by_organization(organization.id)).to include(active_file, inactive_file)
        expect(KnowledgeBaseFile.by_organization(organization.id)).not_to include(other_org_file)
      end
    end

    describe 'RAG status scopes' do
      let!(:pending_file) do
        KnowledgeBaseFile.create(
          name: 'Pending File',
          filename: 'pending.pdf',
          file_url: 'https://example.com/pending.pdf',
          ragie_status: 'pending',
          organization: organization
        )
      end

      let!(:processing_file) do
        KnowledgeBaseFile.create(
          name: 'Processing File',
          filename: 'processing.pdf',
          file_url: 'https://example.com/processing.pdf',
          ragie_status: 'processing',
          organization: organization
        )
      end

      let!(:ready_file) do
        KnowledgeBaseFile.create(
          name: 'Ready File',
          filename: 'ready.pdf',
          file_url: 'https://example.com/ready.pdf',
          ragie_status: 'ready',
          organization: organization
        )
      end

      let!(:failed_file) do
        KnowledgeBaseFile.create(
          name: 'Failed File',
          filename: 'failed.pdf',
          file_url: 'https://example.com/failed.pdf',
          ragie_status: 'failed',
          organization: organization
        )
      end

      describe '.ragie_pending' do
        it 'returns files with pending status' do
          expect(KnowledgeBaseFile.ragie_pending).to include(pending_file)
          expect(KnowledgeBaseFile.ragie_pending).not_to include(processing_file, ready_file, failed_file)
        end
      end

      describe '.ragie_processing' do
        it 'returns files with processing status' do
          expect(KnowledgeBaseFile.ragie_processing).to include(processing_file)
          expect(KnowledgeBaseFile.ragie_processing).not_to include(pending_file, ready_file, failed_file)
        end
      end

      describe '.ragie_ready' do
        it 'returns files with ready status' do
          expect(KnowledgeBaseFile.ragie_ready).to include(ready_file)
          expect(KnowledgeBaseFile.ragie_ready).not_to include(pending_file, processing_file, failed_file)
        end
      end

      describe '.ragie_failed' do
        it 'returns files with failed status' do
          expect(KnowledgeBaseFile.ragie_failed).to include(failed_file)
          expect(KnowledgeBaseFile.ragie_failed).not_to include(pending_file, processing_file, ready_file)
        end
      end

      describe '.ragie_processed' do
        it 'returns files with ready or failed status' do
          expect(KnowledgeBaseFile.ragie_processed).to include(ready_file, failed_file)
          expect(KnowledgeBaseFile.ragie_processed).not_to include(pending_file, processing_file)
        end
      end
    end
  end

  describe 'file type detection' do
    describe '#file_extension' do
      it 'returns the file extension' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.pdf')
        expect(knowledge_base_file.file_extension).to eq('.pdf')
      end

      it 'handles files without extension' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document')
        expect(knowledge_base_file.file_extension).to eq('')
      end

      it 'handles nil filename' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: nil)
        expect(knowledge_base_file.file_extension).to be_nil
      end
    end

    describe '#is_pdf?' do
      it 'returns true for PDF files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.pdf')
        expect(knowledge_base_file.is_pdf?).to be true
      end

      it 'returns false for non-PDF files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.txt')
        expect(knowledge_base_file.is_pdf?).to be false
      end
    end

    describe '#is_text?' do
      it 'returns true for text files' do
        %w[document.txt document.md document.doc document.docx].each do |filename|
          knowledge_base_file = KnowledgeBaseFile.new(filename: filename)
          expect(knowledge_base_file.is_text?).to be true
        end
      end

      it 'returns false for non-text files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.pdf')
        expect(knowledge_base_file.is_text?).to be false
      end
    end

    describe '#is_image?' do
      it 'returns true for image files' do
        %w[image.jpg image.jpeg image.png image.gif image.webp].each do |filename|
          knowledge_base_file = KnowledgeBaseFile.new(filename: filename)
          expect(knowledge_base_file.is_image?).to be true
        end
      end

      it 'returns false for non-image files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.pdf')
        expect(knowledge_base_file.is_image?).to be false
      end
    end

    describe '#file_type' do
      it 'returns pdf for PDF files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.pdf')
        expect(knowledge_base_file.file_type).to eq('pdf')
      end

      it 'returns text for text files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.txt')
        expect(knowledge_base_file.file_type).to eq('text')
      end

      it 'returns image for image files' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'image.jpg')
        expect(knowledge_base_file.file_type).to eq('image')
      end

      it 'returns other for unknown file types' do
        knowledge_base_file = KnowledgeBaseFile.new(filename: 'document.xyz')
        expect(knowledge_base_file.file_type).to eq('other')
      end
    end
  end

  describe '#download_url' do
    it 'returns the file_url when present' do
      knowledge_base_file = KnowledgeBaseFile.new(
        file_url: 'https://example.com/document.pdf'
      )
      expect(knowledge_base_file.download_url).to eq('https://example.com/document.pdf')
    end

    it 'returns nil when file_url is not present' do
      knowledge_base_file = KnowledgeBaseFile.new(file_url: nil)
      expect(knowledge_base_file.download_url).to be_nil
    end

    it 'handles errors gracefully' do
      knowledge_base_file = KnowledgeBaseFile.new(file_url: 'invalid-url')
      allow(knowledge_base_file).to receive(:file_url).and_raise(StandardError.new('Test error'))

      expect(Rails.logger).to receive(:error).with(/Error generating download URL/)
      expect(knowledge_base_file.download_url).to be_nil
    end
  end

  describe 'soft delete' do
    let!(:knowledge_base_file) do
      KnowledgeBaseFile.create(
        name: 'Test File',
        filename: 'test.pdf',
        file_url: 'https://example.com/test.pdf',
        organization: organization
      )
    end

    it 'supports soft delete' do
      expect(knowledge_base_file.discarded?).to be false

      knowledge_base_file.discard!

      expect(knowledge_base_file.discarded?).to be true
      expect(KnowledgeBaseFile.find_by(id: knowledge_base_file.id)).to be_nil
      expect(KnowledgeBaseFile.with_discarded.find(knowledge_base_file.id)).to eq(knowledge_base_file)
    end
  end
end
