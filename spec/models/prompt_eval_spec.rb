# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PromptEval, type: :model do
  let!(:user) { create(:user) }
  let!(:model_template) { create(:model_template) }

  describe 'validations' do
    it 'is valid with valid attributes' do
      prompt_eval = PromptEval.new(
        prompt: 'Test prompt',
        user: user,
        model_template: model_template
      )

      expect(prompt_eval).to be_valid
    end
  end

  describe 'associations' do
    it 'belongs to a user' do
      prompt_eval = create(:prompt_eval, user: user)
      expect(prompt_eval.user).to eq(user)
    end

    it 'has many prompt_eval_results' do
      prompt_eval = create(:prompt_eval)
      create_list(:prompt_eval_result, 3, prompt_eval:)
      expect(prompt_eval.prompt_eval_results.count).to eq(3)
    end
  end
end
