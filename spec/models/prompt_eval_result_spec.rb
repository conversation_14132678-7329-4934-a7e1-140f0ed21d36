# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PromptEvalResult, type: :model do
  let!(:prompt_eval) { create(:prompt_eval) }
  let!(:model_bank) { create(:model_bank) }

  describe 'validations' do
    it 'is valid with valid attributes' do
      prompt_eval_result = PromptEvalResult.new(
        prompt_eval: prompt_eval,
        model_bank: model_bank,
        result_text: 'Test result',
        status: 'pending'
      )

      expect(prompt_eval_result).to be_valid
    end
  end

  describe 'associations' do
    it 'belongs to a prompt_eval' do
      prompt_eval_result = create(:prompt_eval_result, prompt_eval: prompt_eval)
      expect(prompt_eval_result.prompt_eval).to eq(prompt_eval)
    end

    it 'belongs to a model_bank' do
      prompt_eval_result = create(:prompt_eval_result, model_bank: model_bank)
      expect(prompt_eval_result.model_bank).to eq(model_bank)
    end
  end

  describe 'scopes' do
    let!(:completed_result) { create(:prompt_eval_result, :completed) }
    let!(:failed_result) { create(:prompt_eval_result, :failed) }
    let!(:pending_result) { create(:prompt_eval_result, :pending) }

    describe '.completed' do
      it 'returns only completed results' do
        expect(PromptEvalResult.completed).to include(completed_result)
        expect(PromptEvalResult.completed).not_to include(failed_result, pending_result)
      end
    end

    describe '.failed' do
      it 'returns only failed results' do
        expect(PromptEvalResult.failed).to include(failed_result)
        expect(PromptEvalResult.failed).not_to include(completed_result, pending_result)
      end
    end

    describe '.pending' do
      it 'returns only pending results' do
        expect(PromptEvalResult.pending).to include(pending_result)
        expect(PromptEvalResult.pending).not_to include(completed_result, failed_result)
      end
    end
  end

  describe 'enums' do
    it 'defines status enum' do
      expect(PromptEvalResult.statuses).to include('pending', 'completed', 'failed')
    end
  end

  describe 'status methods' do
    let(:completed_result) { create(:prompt_eval_result, :completed) }
    let(:failed_result) { create(:prompt_eval_result, :failed) }
    let(:pending_result) { create(:prompt_eval_result, :pending) }

    describe '#completed?' do
      it 'returns true for completed results' do
        expect(completed_result.completed?).to be true
        expect(failed_result.completed?).to be false
        expect(pending_result.completed?).to be false
      end
    end

    describe '#failed?' do
      it 'returns true for failed results' do
        expect(failed_result.failed?).to be true
        expect(completed_result.failed?).to be false
        expect(pending_result.failed?).to be false
      end
    end

    describe '#pending?' do
      it 'returns true for pending results' do
        expect(pending_result.pending?).to be true
        expect(completed_result.pending?).to be false
        expect(failed_result.pending?).to be false
      end
    end
  end

  describe 'status update methods' do
    let(:pending_result) { create(:prompt_eval_result, :pending) }

    describe '#mark_as_completed!' do
      it 'updates status to completed' do
        pending_result.mark_as_completed!
        expect(pending_result.reload.completed?).to be true
      end
    end

    describe '#mark_as_failed!' do
      it 'updates status to failed with error message' do
        error_message = 'API rate limit exceeded'
        pending_result.mark_as_failed!(error_message)
        expect(pending_result.reload.failed?).to be true
        expect(pending_result.error_message).to eq(error_message)
      end

      it 'updates status to failed without error message' do
        pending_result.mark_as_failed!
        expect(pending_result.reload.failed?).to be true
        expect(pending_result.error_message).to be_nil
      end
    end
  end
end
