# frozen_string_literal: true

FactoryBot.define do
  factory :model_template do
    association :organization
    association :user
    association :template_category
    sequence(:name) { |n| "Template #{n}" }
    description { 'A test template' }
    max_tokens { 2000 }
    temperature { 1.0 }
    model { 'gpt-4o-mini' }
    instruction { 'You are ChatGPT, answer as helpful as possible!' }
    prompt { 'Test prompt' }
    placeholder { 'Enter your question here' }
    verified { false }
    organization_prompt { false }
    draft { true }
    reference_output_url { '' }
    parent_id { nil }
    child_count { 0 }
    in_bank { false }
    bank_added_by { nil }
    bank_added_at { nil }
    bank_notes { nil }
  end

  trait :parent do
    parent_id { nil }
  end

  trait :child do
    association :parent, factory: :model_template
  end

  trait :verified do
    verified { true }
  end

  trait :organization_prompt do
    organization_prompt { true }
  end

  trait :published do
    draft { false }
  end
end
