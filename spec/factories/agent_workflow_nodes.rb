# frozen_string_literal: true

FactoryBot.define do
  factory :agent_workflow_node do
    association :agent_workflow
    association :model_template
    association :knowledge_base_file
    association :model_bank
    name { Faker::Lorem.sentence }
    description { Faker::Lorem.paragraph }
    workflow_type { 'merger' }
    data { {} }
    order_level { 1 }
    rules { Faker::Lorem.paragraph }
    reference_output { Faker::Lorem.paragraph }
  end
end
