# frozen_string_literal: true

FactoryBot.define do
  factory :store_item do
    name { 'Test Store Item' }
    store_item_type { 'one_time_purchase_token' }
    token_amounts { 1000 }
    price { 10 }
    price_decimal { 0 }
    currency { 'USD' }

    trait :premium do
      name { 'Premium Package' }
      token_amounts { 10000 }
      price { 100 }
      price_decimal { 0 }
    end

    trait :basic do
      name { 'Basic Package' }
      token_amounts { 500 }
      price { 5 }
      price_decimal { 0 }
    end

    trait :discarded do
      discarded_at { Time.current }
    end
  end
end
