# frozen_string_literal: true

FactoryBot.define do
  factory :membership do
    association :user
    association :organization
    role { Membership.role_mappings['member'] }
    organization_team_ids { [] }
  end

  trait :admin do
    role { Membership.role_mappings['admin'] }
  end

  trait :super_admin_platform do
    role { Membership.role_mappings['super_admin_platform'] }
  end

  trait :owner_platform do
    role { Membership.role_mappings['owner_platform'] }
  end

  trait :owner do
    role { Membership.role_mappings['owner'] }
  end

  trait :super_admin do
    role { Membership.role_mappings['super_admin'] }
  end

  trait :team_admin do
    role { Membership.role_mappings['team_admin'] }
  end

  trait :member do
    role { Membership.role_mappings['member'] }
  end

  trait :partner_admin do
    role { Membership.role_mappings['partner_admin'] }
  end
end
