# Model Template Parent-Child API Documentation

## Overview

The Model Template API now supports parent-child relationships, allowing you to create parent templates that can be duplicated across multiple organizations as child templates. This replaces the previous Agent Bank implementation.

## Key Features

- **Parent Templates**: Templates that can be duplicated to multiple organizations
- **Child Templates**: Organization-specific copies of parent templates
- **Cross-Organization Duplication**: Duplicate parent templates to multiple organizations
- **Child Counting**: Track how many children a parent template has
- **Filtering**: Filter templates by parent/child status

## API Endpoints

### 1. List Model Templates (Enhanced)

**GET** `/v1/model_templates`

#### Query Parameters

- `parent_only` (boolean, optional): If true, only parent templates are returned
- `child_only` (boolean, optional): If true, only child templates are returned
- `parent_id` (integer, optional): Get all children of a specific parent template
- `child_id` (integer, optional): Get the parent template of a specific child template
- `verified` (boolean, optional): Filter by verification status (true/false)
- `draft` (boolean, optional): Filter by draft status (true/false)
- `organization_prompt` (boolean, optional): Filter by organization prompt status (true/false)
- `template_type` (string, optional): Filter by template type ('default' or 'expert')
- `template_category_id` (integer, optional): Filter by template category ID
- `organization_id` (integer, optional): Filter by organization
- Other existing filters: `search`, `disable_pagination`, `page`, `per_page`

#### Response

```json
{
  "data": [
    {
      "id": 1,
      "name": "Template Name",
      "description": "Description",
      "parent_id": null,
      "child_count": 2,
      "is_parent": true,
      "is_child": false,
      "children": [
        {
          "id": 2,
          "name": "Template Name",
          "organization_id": 2,
          "organization_name": "Organization 2",
          "created_at": "2024-01-01T00:00:00Z"
        }
      ]
    }
  ]
}
```

### 2. Get Model Template Details (Enhanced)

**GET** `/v1/model_templates/:id`

#### Response

Returns the same structure as list, but includes children data if the template is a parent.

### 3. Create Parent Template

**POST** `/v1/parent_templates`

#### Request Body

```json
{
  "name": "Template Name",
  "description": "Description",
  "max_tokens": 1000,
  "temperature": 0.7,
  "instruction": "Instruction",
  "prompt": "Prompt",
  "placeholder": "Placeholder",
  "verified": false,
  "organization_prompt": false,
  "draft": true,
  "reference_output_url": "",
  "template_category_id": 1,
  "organization_team_id": 1
}
```

#### Authorization

- **Required Role:** Platform admin (`admin`, `super_admin_platform`, `owner_platform`)

### 4. Update Parent Template

**PUT** `/v1/parent_templates/:id`

#### Request Body

Same structure as create request.

#### Authorization

- **Required Role:** Platform admin (`admin`, `super_admin_platform`, `owner_platform`)

### 5. Delete Parent Template

**DELETE** `/v1/parent_templates/:id`

#### Authorization

- **Required Role:** Platform admin (`admin`, `super_admin_platform`, `owner_platform`)

**Note:** This will remove the parent_id from all child templates, making them independent templates.

### 6. Duplicate Parent Template

**POST** `/v1/parent_templates/:id/duplicate`

#### Authorization

- **Required Role:** Platform admin (`admin`, `super_admin_platform`, `owner_platform`)

Creates a new parent template as a copy of the existing one.

### 7. Duplicate Parent Template to Organizations

**POST** `/v1/model_templates/:parent_id/duplicate_to_organizations`

#### Request Body

```json
{
  "organization_ids": [2, 3, 4]
}
```

#### Authorization

- **Required Role:** Platform admin (`admin`, `super_admin_platform`, `owner_platform`)

Creates child templates in the specified organizations.

## Key Fields

### Parent-Child Fields

- **`parent_id`**: ID of the parent template (null for parent templates)
- **`child_count`**: Number of children for parent templates
- **`is_parent`**: Boolean indicating if template is a parent
- **`is_child`**: Boolean indicating if template is a child
- **`children`**: Array of child templates (only populated for parent templates)

### Authorization

- **Platform Admins**: Can access all templates across organizations
- **Organization Users**: Can only access templates within their organization
- **Parent Template Operations**: Only platform admins can create/update/delete parent templates

## Usage Examples

### List Parent Templates Only

```bash
GET /v1/model_templates?parent_only=true
```

### List Child Templates Only

```bash
GET /v1/model_templates?child_only=true
```

### Get Children of a Specific Parent

```bash
GET /v1/model_templates?parent_id=123
```

### Get Parent of a Specific Child

```bash
GET /v1/model_templates?child_id=456
```

### Filter by Verification Status

```bash
GET /v1/model_templates?verified=true
```

### Filter by Draft Status

```bash
GET /v1/model_templates?draft=false
```

### Filter by Organization Prompt Status

```bash
GET /v1/model_templates?organization_prompt=true
```

### Filter by Template Type

```bash
GET /v1/model_templates?template_type=expert
```

### Filter by Template Category

```bash
GET /v1/model_templates?template_category_id=123
```

### Combine Multiple Filters

```bash
GET /v1/model_templates?verified=true&draft=false&template_type=expert&template_category_id=123
```

### Create Parent Template

```bash
POST /v1/parent_templates
Content-Type: application/json

{
  "name": "Customer Support Agent",
  "description": "A helpful customer support agent",
  "max_tokens": 1000,
  "temperature": 0.7,
  "instruction": "You are a helpful customer support agent",
  "prompt": "Help the customer with their inquiry"
}
```

### Duplicate to Organizations

```bash
POST /v1/model_templates/1/duplicate_to_organizations
Content-Type: application/json

{
  "organization_ids": [2, 3, 4]
}
```

## Migration from Agent Bank

The Agent Bank implementation has been removed and replaced with this enhanced Model Template functionality. All parent-child relationships are now handled through the Model Template API.

### Key Changes

1. **Removed Files**:

   - `app/services/agent_bank_service.rb`
   - `app/controllers/v1/agent_bank_controller.rb`
   - `app/outputs/v1/agent_bank_output.rb`
   - `app/inputs/v1/agent_bank_*.rb`
   - All agent bank specs and documentation

2. **Enhanced Files**:

   - `app/services/model_template_service.rb` - Added parent-child methods
   - `app/controllers/v1/model_templates_controller.rb` - Added new endpoints
   - `app/outputs/v1/model_template_output.rb` - Added parent-child fields
   - `config/routes.rb` - Updated routes

3. **Database Schema**:
   - `parent_id` and `child_count` columns already exist in `model_templates` table
   - No additional migrations needed

## Error Handling

### Common Errors

- **403 Forbidden**: Insufficient permissions for parent template operations
- **422 Unprocessable Entity**: Invalid request data
- **404 Not Found**: Template not found
- **400 Bad Request**: Attempting to duplicate non-parent template

### Error Response Format

```json
{
  "error": "Error message",
  "details": {
    "field": ["validation error"]
  }
}
```
