# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

puts '🌱 Seeding database with test data...'

# Create Organizations
puts 'Creating organizations...'
organizations = []
3.times do |i|
  org = Organization.create!(
    name: "BrandRev Organization #{i + 1}",
    logo_url: "https://example.com/logo#{i + 1}.png",
    code: "ORG#{SecureRandom.hex(4).upcase}",
    superuser_privilege: i == 0 # First org gets superuser privilege
  )
  organizations << org
  puts "  ✓ Created organization: #{org.name}"
end

# Create Organizations Plans Thresholds
puts 'Creating organizations plans thresholds...'
organizations.each_with_index do |org, i|
  threshold = OrganizationsPlansThreshold.create!(
    organization: org,
    purchased_credits: i == 0 ? 1000.0 : 500.0, # First org gets more credits
    max_workspaces: 10,
    max_members: 50,
    monthly_credits_refresh: 1000,
    refresh_date: 1, # 1st of the month
    remaining_monthly_credits: i == 0 ? 1000.0 : 500.0
  )
  puts "  ✓ Created plan threshold for #{org.name}: #{threshold.purchased_credits} credits"
end

# Create Workspaces
puts 'Creating workspaces...'
workspaces = []
organizations.each_with_index do |org, _i|
  workspace = Workspace.create!(
    name: "#{org.name} Workspace",
    max_workspaces: 10,
    max_members: 50,
    organization_id: org.id
  )
  workspaces << workspace
  puts "  ✓ Created workspace: #{workspace.name}"
end

# Create Users
puts 'Creating users...'
users = []
5.times do |i|
  user = User.create!(
    display_name: "Test User #{i + 1}",
    email: "testuser#{i + 1}@brandrev.ai",
    password: '********'
  )
  users << user
  puts "  ✓ Created user: #{user.display_name} (#{user.email})"
end

# Create Template Categories
puts 'Creating template categories...'
categories = []
category_names = ['Customer Support', 'Sales', 'Marketing', 'Technical', 'General']
category_names.each_with_index do |name, i|
  category = TemplateCategory.create!(
    name: name,
    validated: i < 3, # First 3 categories are validated
    general_category: i < 2, # First 2 are general categories
    organization: organizations.first
  )
  categories << category
  puts "  ✓ Created category: #{category.name}"
end

# Create Organization Teams
puts 'Creating organization teams...'
teams = []
team_names = %w[Engineering Sales Marketing Support]
team_names.each_with_index do |name, i|
  team = OrganizationTeam.create!(
    name: name,
    organization: organizations[i % organizations.length]
  )
  teams << team
  puts "  ✓ Created team: #{team.name} (#{team.organization.name})"
end

# Create Memberships with different roles
puts 'Creating memberships...'
memberships = []

# Platform admins (can access agent bank)
users[0..2].each_with_index do |user, i|
  membership = Membership.create!(
    user: user,
    organization: organizations[i % organizations.length],
    role: Membership.role_mappings[%w[admin super_admin_platform owner_platform][i]],
    organization_team_ids: [teams[i % teams.length].id]
  )
  memberships << membership
  puts "  ✓ Created membership: #{user.display_name} -> #{membership.organization.name} (#{membership.role})"
end

# Regular members (cannot access agent bank)
users[3..4].each do |user|
  membership = Membership.create!(
    user: user,
    organization: organizations.last,
    role: Membership.role_mappings['member'],
    organization_team_ids: []
  )
  memberships << membership
  puts "  ✓ Created membership: #{user.display_name} -> #{membership.organization.name} (member)"
end

# Create Workspaces Memberships
puts 'Creating workspaces memberships...'
memberships.each_with_index do |membership, i|
  workspace = workspaces[i % workspaces.length]
  WorkspacesMembership.create!(
    workspace_id: workspace.id,
    membership_id: membership.id,
    role: 'admin'
  )
  puts "  ✓ Created workspace membership: #{membership.user.display_name} -> #{workspace.name}"
end

# Create Model Templates with Parent-Child Relationships
puts 'Creating model templates...'

# Parent Templates (Agent Bank Templates)
parent_templates = []
parent_template_data = [
  {
    name: 'Customer Support Assistant',
    description: 'A helpful assistant for customer support queries',
    max_tokens: 2000,
    temperature: 0.7,
    model: 'gpt-4o-mini',
    instruction: 'You are a helpful customer support assistant. Be polite, professional, and solve customer issues efficiently.',
    prompt: "Customer inquiry: {customer_question}\n\nPlease provide a helpful response that addresses their concern.",
    placeholder: 'Enter customer question here...',
    verified: true,
    organization_prompt: false,
    draft: false,
    template_category: categories[0] # Customer Support
  },
  {
    name: 'Sales Pitch Generator',
    description: 'Generate compelling sales pitches for different products',
    max_tokens: 1500,
    temperature: 0.8,
    model: 'gpt-4o-mini',
    instruction: 'You are a sales expert. Create compelling sales pitches that highlight benefits and address pain points.',
    prompt: "Product: {product_name}\nTarget audience: {target_audience}\nKey benefits: {key_benefits}\n\nGenerate a sales pitch:",
    placeholder: 'Enter product details...',
    verified: true,
    organization_prompt: false,
    draft: false,
    template_category: categories[1] # Sales
  },
  {
    name: 'Marketing Content Writer',
    description: 'Create engaging marketing content for various channels',
    max_tokens: 2500,
    temperature: 0.9,
    model: 'gpt-4o-mini',
    instruction: 'You are a creative marketing writer. Create engaging, persuasive content that drives action.',
    prompt: "Content type: {content_type}\nBrand voice: {brand_voice}\nTarget audience: {target_audience}\nKey message: {key_message}\n\nWrite marketing content:",
    placeholder: 'Enter marketing brief...',
    verified: true,
    organization_prompt: false,
    draft: false,
    template_category: categories[2] # Marketing
  },
  {
    name: 'Technical Documentation Helper',
    description: 'Assist with technical documentation and explanations',
    max_tokens: 3000,
    temperature: 0.5,
    model: 'gpt-4o-mini',
    instruction: 'You are a technical writer. Create clear, accurate technical documentation and explanations.',
    prompt: "Technical topic: {topic}\nAudience level: {audience_level}\nKey points to cover: {key_points}\n\nCreate technical documentation:",
    placeholder: 'Enter technical topic...',
    verified: false,
    organization_prompt: false,
    draft: true,
    template_category: categories[3] # Technical
  }
]

parent_template_data.each_with_index do |data, i|
  template = ModelTemplate.create!(
    name: data[:name],
    description: data[:description],
    max_tokens: data[:max_tokens],
    temperature: data[:temperature],
    model: data[:model],
    instruction: data[:instruction],
    prompt: data[:prompt],
    placeholder: data[:placeholder],
    verified: data[:verified],
    organization_prompt: data[:organization_prompt],
    draft: data[:draft],
    reference_output_url: '',
    organization: organizations[i % organizations.length],
    user: users[i % users.length],
    template_category: data[:template_category],
    organization_team: teams[i % teams.length],
    parent_id: nil, # Parent template
    child_count: 0
  )
  parent_templates << template
  puts "  ✓ Created parent template: #{template.name} (#{template.organization.name})"
end

# Child Templates (Organization-specific versions)
puts 'Creating child templates...'
child_templates = []

parent_templates.each_with_index do |parent, parent_index|
  # Create 1-2 child templates for each parent
  (1..2).each do |child_num|
    child_org = organizations[(parent_index + child_num) % organizations.length]
    child_user = users[(parent_index + child_num) % users.length]

    child = ModelTemplate.create!(
      name: "#{parent.name} - #{child_org.name}",
      description: "#{parent.description} (Customized for #{child_org.name})",
      max_tokens: parent.max_tokens,
      temperature: parent.temperature,
      model: parent.model,
      instruction: parent.instruction,
      prompt: parent.prompt,
      placeholder: parent.placeholder,
      verified: parent.verified,
      organization_prompt: true, # Child templates are org-specific
      draft: parent.draft,
      reference_output_url: '',
      organization: child_org,
      user: child_user,
      template_category: parent.template_category,
      organization_team: teams[(parent_index + child_num) % teams.length],
      parent_id: parent.id, # Child of parent template
      child_count: 0
    )
    child_templates << child
    puts "  ✓ Created child template: #{child.name} (Parent: #{parent.name})"
  end
end

# Update child counts for parent templates
puts 'Updating child counts...'
parent_templates.each do |parent|
  parent.update_child_count!
  puts "  ✓ Updated #{parent.name}: #{parent.child_count} children"
end

# Create some Model Template Variables for parent templates
puts 'Creating template variables...'
parent_templates.each_with_index do |template, i|
  variable_data = case i
                  when 0 # Customer Support
                    [
                      { name: 'customer_question', description: "The customer's question or issue", weight: 'high',
                        references: '' },
                      { name: 'tone', description: 'Response tone (formal, casual, empathetic)', weight: 'medium',
                        references: '' }
                    ]
                  when 1 # Sales
                    [
                      { name: 'product_name', description: 'Name of the product to pitch', weight: 'high',
                        references: '' },
                      { name: 'target_audience', description: 'Target audience for the pitch', weight: 'high',
                        references: '' },
                      { name: 'key_benefits', description: 'Key benefits of the product', weight: 'high',
                        references: '' }
                    ]
                  when 2 # Marketing
                    [
                      { name: 'content_type', description: 'Type of content (blog, social, email)', weight: 'high',
                        references: '' },
                      { name: 'brand_voice', description: 'Brand voice and tone', weight: 'medium', references: '' },
                      { name: 'target_audience', description: 'Target audience', weight: 'high', references: '' },
                      { name: 'key_message', description: 'Key message to convey', weight: 'high', references: '' }
                    ]
                  when 3 # Technical
                    [
                      { name: 'topic', description: 'Technical topic to document', weight: 'high', references: '' },
                      { name: 'audience_level', description: 'Audience technical level (beginner, intermediate, advanced)',
                        weight: 'medium', references: '' },
                      { name: 'key_points', description: 'Key points to cover', weight: 'high', references: '' }
                    ]
                  end

  variable_data.each_with_index do |var_data, var_index|
    ModelTemplateVariable.create!(
      name: var_data[:name],
      description: var_data[:description],
      weight: var_data[:weight],
      references: var_data[:references],
      variable_reference_url: '',
      order: var_index,
      model_template: template
    )
  end
  puts "  ✓ Created #{variable_data.length} variables for #{template.name}"
end

# Create some Model Template Inputs for parent templates
puts 'Creating template inputs...'
parent_templates.each_with_index do |template, i|
  input_data = case i
               when 0 # Customer Support
                 [
                   { name: 'customer_context', description: 'Additional customer context' }
                 ]
               when 1 # Sales
                 [
                   { name: 'competitor_info', description: 'Competitor information' },
                   { name: 'pricing_info', description: 'Pricing information' }
                 ]
               when 2 # Marketing
                 [
                   { name: 'call_to_action', description: 'Desired call to action' }
                 ]
               when 3 # Technical
                 [
                   { name: 'examples_needed', description: 'Number of examples needed' }
                 ]
               end

  input_data.each_with_index do |input_data, input_index|
    ModelTemplateIn.create!(
      name: input_data[:name],
      description: input_data[:description],
      input_reference_url: '',
      order: input_index,
      model_template: template
    )
  end
  puts "  ✓ Created #{input_data.length} inputs for #{template.name}"
end

# Create Models (AI Model Configurations)
puts 'Creating models...'
models = []

# Create models for each organization
organizations.each_with_index do |org, org_index|
  model_data = [
    {
      name: "#{org.name} - GPT-4o",
      model: 'openai/gpt-4o',
      instruction: 'You are a helpful AI assistant for this organization.',
      temperature: 0.7,
      max_tokens: 4000,
      file_search: true,
      code_interpreter: false
    },
    {
      name: "#{org.name} - GPT-4o Mini",
      model: 'openai/gpt-4o-mini',
      instruction: 'You are a helpful AI assistant for this organization.',
      temperature: 0.8,
      max_tokens: 2000,
      file_search: true,
      code_interpreter: false
    },
    {
      name: "#{org.name} - DeepSeek R1",
      model: 'deepseek/deepseek-r1',
      instruction: 'You are a helpful AI assistant for this organization.',
      temperature: 0.6,
      max_tokens: 3000,
      file_search: false,
      code_interpreter: true
    }
  ]

  model_data.each_with_index do |data, _model_index|
    model = Model.create!(
      name: data[:name],
      model: data[:model],
      instruction: data[:instruction],
      temperature: data[:temperature],
      max_tokens: data[:max_tokens],
      file_search: data[:file_search],
      code_interpreter: data[:code_interpreter],
      organization: org,
      model_template: parent_templates[org_index % parent_templates.length] # Link to a parent template
    )
    models << model
    puts "  ✓ Created model: #{model.name} (#{model.model})"
  end
end

# Create some additional models linked to specific templates
parent_templates.each_with_index do |template, _i|
  model = Model.create!(
    name: "#{template.name} - Specialized Model",
    model: 'google/gemini-2.5-pro-preview',
    instruction: template.instruction,
    temperature: template.temperature,
    max_tokens: template.max_tokens,
    file_search: true,
    code_interpreter: false,
    organization: template.organization,
    model_template: template
  )
  models << model
  puts "  ✓ Created specialized model: #{model.name} for #{template.name}"
end

puts ''
puts '🎉 Database seeded successfully!'
puts ''
puts '📊 Summary:'
puts "  • #{organizations.length} organizations"
puts "  • #{users.length} users"
puts "  • #{categories.length} template categories"
puts "  • #{teams.length} organization teams"
puts "  • #{memberships.length} memberships"
puts "  • #{workspaces.length} workspaces"
puts "  • #{workspaces.length} workspaces memberships"
puts "  • #{parent_templates.length} parent templates"
puts "  • #{child_templates.length} child templates"
puts "  • #{models.length} models"
puts ''
puts '🔑 Test Accounts (Platform Admins - can access Parent Templates):'
users[0..2].each_with_index do |user, i|
  puts "  • #{user.email} (password: ********) - #{memberships[i].role}"
end
puts ''
puts '🚀 To test the Parent-Child Template API:'
puts '  1. Start your Rails server'
puts '  2. Login with any of the platform admin accounts above'
puts '  3. Access: GET /v1/model_templates'
puts '  4. Try filtering: GET /v1/model_templates?parent_only=true'
puts '  5. Create parent template: POST /v1/parent_templates'
puts '  6. Duplicate to organizations: POST /v1/model_templates/:id/duplicate_to_organizations'
puts ''
