class CreateAgentWorkflowNodes < ActiveRecord::Migration[7.0]
  def change
    create_table :agent_workflow_nodes do |t|
      t.references :agent_workflow, null: false, foreign_key: true
      t.references :model_template, null: true, foreign_key: true
      t.references :model, null: true, foreign_key: true

      t.string :name
      t.string :description
      t.string :type, default: 'merger', null: false
      t.jsonb :data, default: {}
      t.datetime :discarded_at, index: true
      t.timestamps
    end
  end
end
