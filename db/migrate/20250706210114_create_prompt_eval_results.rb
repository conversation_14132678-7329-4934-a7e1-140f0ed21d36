# frozen_string_literal: true

class CreatePromptEvalResults < ActiveRecord::Migration[7.0]
  def change
    create_table :prompt_eval_results do |t|
      t.references :prompt_eval, null: false, foreign_key: true
      t.references :model_bank, null: false, foreign_key: true

      t.string :result_text, null: false
      t.string :status, null: false, default: 'pending'
      t.string :error_message
      t.jsonb :response_raw, default: {}

      t.datetime :discarded_at, index: true

      t.timestamps
    end
  end
end
