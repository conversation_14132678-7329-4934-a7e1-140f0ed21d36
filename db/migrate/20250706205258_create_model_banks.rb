class CreateModelBanks < ActiveRecord::Migration[7.0]
  def change
    create_table :model_banks do |t|
      t.string :name, null: false, index: true
      t.string :code, null: false
      t.float :input_rate, null: false, default: 0
      t.float :output_rate, null: false, default: 0
      t.float :web_search_rate, null: false, default: 0
      t.float :image_rate, null: false, default: 0
      t.float :file_rate, null: false, default: 0

      t.string :status, null: false, default: 'active', index: true

      t.datetime :discarded_at, index: true

      t.timestamps
    end
  end
end
