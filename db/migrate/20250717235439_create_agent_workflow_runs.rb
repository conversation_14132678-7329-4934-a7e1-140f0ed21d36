class CreateAgentWorkflowRuns < ActiveRecord::Migration[7.0]
  def change
    create_table :agent_workflow_runs do |t|
      t.references :agent_workflow, null: false, foreign_key: true
      t.string :status, null: false, default: 'pending'
      t.jsonb :data, default: {}
      t.string :error_message
      t.string :error_code

      t.datetime :started_at
      t.datetime :completed_at
      t.datetime :failed_at
      t.datetime :discarded_at, index: true

      t.timestamps
    end
  end
end
