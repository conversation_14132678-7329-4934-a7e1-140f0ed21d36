class AddBankFieldsToModelTemplates < ActiveRecord::Migration[7.0]
  def change
    add_column :model_templates, :in_bank, :boolean, default: false
    add_column :model_templates, :bank_added_by, :string
    add_column :model_templates, :bank_added_at, :datetime
    add_column :model_templates, :bank_notes, :text
    
    add_index :model_templates, :in_bank
    add_index :model_templates, :bank_added_by
  end
end 