class CreatePartnerAssignedModelTemplates < ActiveRecord::Migration[7.0]
  def change
    create_table :partner_assigned_model_templates do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :model_template, null: false, foreign_key: true      
      t.datetime :discarded_at, index: true
      t.timestamps

      t.index [:organization_id, :model_template_id], unique: true, name: 'index_unique_organization_model_template_partners'
    end
  end
end
