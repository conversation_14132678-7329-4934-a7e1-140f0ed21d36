class CreateKnowledgeBaseFiles < ActiveRecord::Migration[7.0]
  def change
    create_table :knowledge_base_files do |t|
      t.bigint :organization_id, null: false
      t.string :name, null: false
      t.text :description
      t.string :file_url
      t.string :filename, null: false
      t.string :content_type
      t.bigint :file_size
      t.boolean :is_active, default: true
      t.datetime :discarded_at
      t.string :ragie_document_id
      t.string :ragie_status, default: 'pending'
      t.datetime :ragie_processed_at
      t.text :ragie_error_message

      t.timestamps
    end

    add_index :knowledge_base_files, :organization_id
    add_index :knowledge_base_files, :discarded_at
    add_index :knowledge_base_files, [:organization_id, :is_active]
    add_index :knowledge_base_files, [:organization_id, :ragie_status]
    add_index :knowledge_base_files, :ragie_document_id
    add_index :knowledge_base_files, :ragie_status
    
    add_foreign_key :knowledge_base_files, :organizations, column: :organization_id
  end
end
