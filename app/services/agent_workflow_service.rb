# frozen_string_literal: true

class AgentWorkflowService < AppService
  def initialize(user)
    super

    @agent_workflows = AgentWorkflows.new
  end

  def index(params)
    organization_id = params[:organization_id]

    if organization_id
      target_organization = Organization.find(organization_id)
      ownership = organization_ownership(target_organization)
      params[:organization_id] = user_allowed_org_ids unless ownership
    else
      params[:organization_id] = user_allowed_org_ids unless platform_admin?
    end

    @agent_workflows.filter(params)
  end

  def show(id)
    agent_workflow = AgentWorkflow.find(id)

    ownership = organization_ownership(agent_workflow.organization)

    authorize! ownership, on_error: 'You are not allowed to view this agent workflow'

    nodes = AgentWorkflowNode.where(agent_workflow_id: agent_workflow.id)

    knowledge_base_file_ids = []
    model_bank_ids = []
    model_template_ids = []

    nodes.each do |node|
      knowledge_base_file_ids << node.knowledge_base_file_id
      model_bank_ids << node.model_bank_id
      model_template_ids << node.model_template_id
    end

    model_templates = ModelTemplate.where(id: model_template_ids)
    model_banks = ModelBank.where(id: model_bank_ids)
    knowledge_base_files = KnowledgeBaseFile.where(id: knowledge_base_file_ids)

    model_template_ids = model_templates.map(&:id)
    model_template_inputs = ModelTemplateIn.where(model_template_id: model_template_ids)

    OpenStruct.new(
      agent_workflow: agent_workflow,
      nodes: nodes,
      model_templates: model_templates,
      model_banks: model_banks,
      knowledge_base_files: knowledge_base_files,
      model_template_inputs: model_template_inputs
    )
  end

  def create(params)
    organization_id = params[:organization_id]
    params[:created_by_id] = @user.id
    model_template_ids = params.delete(:model_template_ids)

    target_organization = Organization.find(organization_id)
    ownership = organization_ownership(target_organization)

    authorize! ownership, on_error: 'You are not allowed to create agent workflow on this organization'

    agent_workflow = AgentWorkflow.create!(params)

    process_node_creation!(agent_workflow, model_template_ids) if model_template_ids.present?
    agent_workflow
  end

  def update(id, params)
    agent_workflow = AgentWorkflow.find(id)
    organization_id = agent_workflow.organization_id

    target_organization = Organization.find(organization_id)
    ownership = organization_ownership(target_organization)

    authorize! ownership, on_error: 'You are not allowed to update this agent workflow'

    agent_workflow.update!(params)

    agent_workflow.reload
    agent_workflow
  end

  def destroy(id)
    agent_workflow = AgentWorkflow.find(id)
    organization_id = agent_workflow.organization_id

    target_organization = Organization.find(organization_id)
    ownership = organization_ownership(target_organization)

    authorize! ownership, on_error: 'You are not allowed to delete this agent workflow'

    agent_workflow.discard!

    OpenStruct.new(
      id: id
    )
  end

  private

  def process_node_creation!(agent_workflow, model_template_ids)
    model_templates = ModelTemplate.where(id: model_template_ids)

    completed_template = model_templates.size == model_template_ids.size
    exist! completed_template, on_error: 'Some model templates are not found'

    default_mode_bank = ModelBank.all.first

    records = []
    model_templates.each do |model_template|
      records << AgentWorkflowNode.new(
        agent_workflow_id: agent_workflow.id,
        model_template_id: model_template.id,
        model_bank_id: default_mode_bank.id,
        workflow_type: 'agent',
        order_level: 0,
        name: model_template.name,
        description: model_template.description
      )
    end

    model_template_creation_params = {
      name: agent_workflow.name,
      organization_id: agent_workflow.organization_id,
      model: default_mode_bank.code,
      prompt: '',
      placeholder: '',
      user: @user,
      template_type: 'workflow',
      description: agent_workflow.description,
      draft: false
    }

    merger_template = ModelTemplate.create!(model_template_creation_params)

    Model.create!(
      model: 'openai/gpt-4o-mini',
      name: "#{merger_template.name} Merger",
      max_tokens: 16_000,
      temperature: 0.8,
      instruction: 'You are an AI assistant, answer as helpful as possible!',
      model_template_id: merger_template.id
    )

    records << AgentWorkflowNode.new(
      agent_workflow_id: agent_workflow.id,
      model_template_id: merger_template.id,
      model_bank_id: default_mode_bank.id,
      workflow_type: 'merger',
      order_level: 99,
      name: agent_workflow.name,
      description: agent_workflow.description
    )

    AgentWorkflowNode.import records
  end
end
