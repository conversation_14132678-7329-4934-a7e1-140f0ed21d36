# frozen_string_literal: true

class ModelRatingService < AppService
  def initialize(user)
    super
    @model_ratings = ModelRatings.new
  end

  def create(params)
    params = params.merge(user_id: @user.id)
    chat_id = params.delete(:chat_id)

    message_id = Message.where(chat_id:, sender: 'assistant').order(created_at: :desc).first.id
    params[:message_id] = message_id

    ModelRating.create!(params)
  end

  def update(id, params)
    params.delete(:user_id)
    params.delete(:model_template_id)

    model_rating = ModelRating.find(id)
    authorize! rating_ownership(model_rating)

    model_rating.update(params)
    model_rating
  end

  def index(query_params)
    filter = query_params.slice(
      :model_template_id,
      :user_id,
      :disable_pagination,
      :organization_id,
      :template_category_id,
      :sort_column,
      :sort_direction,
      :model_template_name
    )

    empty_response = OpenStruct.new(
      model_ratings: [],
      users: [],
      model_templates: [],
      template_categories: [],
      messages: [],
      memberships: [],
      organizations: []
    )

    return empty_response if !platform_admin? && filter[:organization_id].blank?

    filter[:organization_id] = @user.membership.organization_id unless platform_admin?

    ratings = @model_ratings.filter(filter)

    user_ids, model_template_ids, message_ids = ratings.pluck(:user_id, :model_template_id,
                                                              :message_id).transpose

    users = User.where(id: user_ids)
    memberships = Membership.where(user_id: user_ids)

    model_templates = ModelTemplate.where(id: model_template_ids)
    organization_ids = model_templates.map(&:organization_id).uniq
    organizations = Organization.where(id: organization_ids)

    template_category_ids = model_templates.map(&:template_category_id)
    template_categories = TemplateCategory.where(id: template_category_ids)

    messages = Message.joins(chat: :model).where(id: message_ids).select('messages.id, models.model')

    OpenStruct.new(
      model_ratings: ratings,
      users:,
      model_templates:,
      template_categories:,
      messages:,
      memberships:,
      organizations:
    )
  end

  def destroy(id)
    model_rating = ModelRating.find(id)
    authorize! rating_ownership(model_rating)

    model_rating.discard!
  end

  def model_rating_results(query_params)
    results = index(query_params)

    csv_output(results)
  end

  private

  def csv_output(results)
    CSV.generate(headers: true) do |csv|
      csv << ['Agent Name', 'Agent Category', 'Rating', 'Organization', 'User', 'Role', 'Rating', 'Model Used', 'Comment',
              'Date Commented']

      ratings = results[:model_ratings]
      users = results[:users]
      model_templates = results[:model_templates]
      template_categories = results[:template_categories]
      messages = results[:messages]
      memberships = results[:memberships]
      organizations = results[:organizations]

      ratings.each do |rating|
        user = users.find { |u| u.id.to_s == rating.user_id.to_s }
        model_template = model_templates.find { |mt| mt.id == rating.model_template_id }
        template_category = template_categories.find { |tc| tc.id == model_template&.template_category_id }
        message = messages.find { |m| m.id == rating&.message_id }
        membership = memberships.find { |m| m.user_id == user&.id }
        organization = organizations.find { |mt| mt.id.to_s == model_template&.organization_id.to_s }

        csv << [model_template&.name, template_category&.name, rating&.rating, organization&.name, user&.display_name,
                membership&.membership_role, message&.model, rating&.comment, rating&.created_at]
      end
    end
  end

  def rating_ownership(rating)
    @user.id == rating.user_id
  end
end
