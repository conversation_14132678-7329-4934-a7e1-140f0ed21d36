# frozen_string_literal: true

module External
  class Ragie
    def initialize(organization_id)
      organization = Organization.find(organization_id)
      @scope = "#{Rails.env}-#{organization.id}"
    end

    def create_document_from_url(params)
      params[:partition] = @scope

      input = CreateDocumentUrlInput.new(params)

      data = JSON.dump(input.output)
      uri_path = '/documents/url'
      response = http.post uri_path, data

      JSON.parse(response.body)
    end

    def update_document_from_url(params)
      params[:partition] = @scope

      input = UpdateDocumentUrlInput.new(params)

      validated_data = input.output
      document_id = validated_data.delete!(:document_id)
      data = JSON.dump(validated_data)

      uri_path = "/documents/#{document_id}/url"
      response = http.put uri_path, data

      JSON.parse(response.body)
    end

    def delete_document(document_id)
      uri_path = "/documents/#{document_id}"
      response = http.delete uri_path

      JSON.parse(response.body)
    end

    def get_document(document_id)
      uri_path = "/documents/#{document_id}"
      response = http.get uri_path

      JSON.parse(response.body)
    end

    def get_document_content(document_id)
      uri_path = "/documents/#{document_id}/content"
      response = http.get uri_path

      JSON.parse(response.body)
    end

    def retrieve(query, document_ids: [], top_k: 8)
      data = {
        query:,
        partition: @scope,
        filter: {
          document_id: { '$in' => document_ids }
        },
        top_k:
      }

      uri_path = '/retrievals'
      response = http.post uri_path, JSON.dump(data)

      JSON.parse(response.body)
    end

    private

    def http
      @http ||= Faraday.new(options) do |faraday|
        faraday.use Faraday::Response::RaiseError
      end
    end

    def options
      {
        headers:,
        url:
      }
    end

    def headers
      {
        'Authorization' => "Bearer #{api_key}",
        'content-type' => 'application/json',
        'accept' => 'application/json',
        'partition' => @scope.to_s
      }
    end

    def url
      ENV['RAGIE_AI_API_URL'] || 'https://api.ragie.ai'
    end

    def api_key
      ENV.fetch('RAGIE_AI_API_KEY', nil)
    end

    class CreateDocumentUrlInput < ::ApplicationInput
      required(:external_id)
      required(:url)
      required(:partition)
      optional(:mode).string(default: 'hi_res')
      optional(:metadata)
    end

    class UpdateDocumentUrlInput < ::ApplicationInput
      required(:url)
      required(:partition)
      optional(:mode).string(default: 'hi_res')
      optional(:metadata)
    end
  end
end
