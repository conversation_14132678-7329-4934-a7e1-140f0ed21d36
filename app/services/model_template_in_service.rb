# frozen_string_literal: true

class ModelTemplateInService < AppService
  def initialize(user)
    super

    @openai_service = OpenaiService.new(@user)
  end

  def create(params)
    is_v2 = as_boolean(params.delete(:v2))

    template = ModelTemplate.find_by(id: params[:model_template_id])
    authorize! template_ownership(template)

    template_models = Model.where(model_template_id: params[:model_template_id])
    model = template_models.where.not(openai_assistant_id: nil).first unless is_v2
    model ||= template_models.first
    exist! model.present?, on_error: 'Model not found'

    ModelTemplateIn.create!(params)
  end

  def update(id, params)
    template_input = ModelTemplateIn.find(id)

    template = template_input.model_template
    authorize! template_ownership(template)

    Model.find_by!(model_template_id: template_input.model_template_id)

    template_input.update!(params)

    template_input
  end

  def index(query_params)
    model_template_ins = ::ModelTemplateIns.new

    filter = query_params.slice(
      :model_template_id,
      :search,
      :organization_id
    )

    # If user is not admin, force their organization_id
    # If user is admin, use organization_id from params if provided
    unless platform_admin?
      filter = filter.merge(
        organization_id: @user.membership&.organization_id
      )
    end

    filtered = model_template_ins.filter(filter)

    OpenStruct.new(
      template_instruction_inputs: filtered
    )
  end

  def destroy(id)
    template_input = ModelTemplateIn.find(id)

    template = template_input.model_template
    authorize! template_ownership(template)
    template_input.discard!
  end

  private

  def template_ownership(template)
    return true if platform_admin?
    return true if @user.membership&.organization_id == template.organization_id

    false
  end
end
