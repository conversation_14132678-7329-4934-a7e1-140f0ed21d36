# frozen_string_literal: true

class AgentWorkflowRunService < AppService
  def create(params)
    agent_workflow_id = params[:agent_workflow_id]
    query_list = params[:query_list]
    chat_id = params[:chat_id]

    AgentWorkflow.find(agent_workflow_id)
    Chat.find(chat_id)

    agent_workflow_run = AgentWorkflowRun.new

    AgentWorkflowRun.transaction do
      agent_workflow_run = AgentWorkflowRun.create!(
        agent_workflow_id: agent_workflow_id,
        data: {
          query_list: query_list
        },
        user_id: @user.id
      )

      records = []

      nodes = AgentWorkflowNode.includes(model_template: :model_template_ins)
                               .where(agent_workflow_id: agent_workflow_id)

      agent_nodes = nodes.filter { |node| node.workflow_type == 'agent' }
      merger_node = nodes.find { |node| node.workflow_type == 'merger' }

      nodes.each do |node|
        current_template = node.model_template
        current_prompt = if node.workflow_type == 'agent'
                           agent_system_prompt(current_template)
                         else
                           merger_system_prompt(
                             merger_node, agent_nodes
                           )
                         end

        current_input_keys = current_template.model_template_ins.map(&:id)
        current_content = current_input_keys.map { |key| query_list[key] }.join("\n")

        records << {
          agent_workflow_node_id: node.id,
          agent_workflow_run_id: agent_workflow_run.id,
          user_id: @user.id,
          data: {
            name: current_template.name,
            temperature: current_template.temperature,
            system_prompt: current_prompt,
            content: current_content
          }
        }
      end

      AgentWorkflowNodeRun.import records
      AgentWorkflowRunJob.perform_later(agent_workflow_run.id)
    end

    agent_workflow_run
  end

  def stream(sse, params)
    create_params = {
      agent_workflow_id: params[:workflow_id],
      query_list: params[:query_list],
      chat_id: params[:chat]&.id
    }

    @assistant_message = params[:assistant_message_obj]
    @user_message = params[:user_message_obj]
    @chat = params[:chat]

    agent_workflow_run = create(create_params)

    hard_limit = 30
    counter = 0

    break_flag = false
    while break_flag == false
      sleep 10

      counter += 1
      break_flag = true if agent_workflow_run.reload.status.in?(%w[completed failed])
      raise 'Agent workflow run timeout' if counter >= hard_limit
    end

    node_runs = AgentWorkflowNodeRun.joins(:agent_workflow_node).where(agent_workflow_run_id: agent_workflow_run.id).select('agent_workflow_node_runs.*, agent_workflow_nodes.workflow_type')
    merger_run = node_runs.find { |node_run| node_run.workflow_type == 'merger' }
    agent_runs = node_runs.filter { |node_run| node_run.workflow_type == 'agent' }

    usages = {
      token_input: 0,
      token_output: 0,
      input_usage: 0,
      output_usage: 0
    }

    agent_runs.each do |run|
      usages[:token_input] += run.usage_metadata['prompt_tokens'] || 0
      usages[:token_output] += run.usage_metadata['completion_tokens'] || 0
      usages[:input_usage] += run.data.dig('credit_usage', 'input') || 0
      usages[:output_usage] += run.data.dig('credit_usage', 'output') || 0
    end

    random_id = SecureRandom.uuid
    response = merger_run.data['response']
    sse.write(format_output(response, random_id))

    @user_message.token_used = usages[:token_input]
    @user_message.credits_used = usages[:input_usage]
    @user_message.save!

    @assistant_message.token_used = usages[:token_output]
    @assistant_message.credits_used = usages[:output_usage]
    @assistant_message.content = response
    @assistant_message.save!
  end

  private

  def format_output(chunk, id)
    {
      id:,
      object: 'thread.message.delta',
      delta: {
        content: [{
          index: 0,
          type: 'text',
          text: { value: chunk }
        }],
        annotations: []
      },
      chat_id: @chat.id,
      message_id: @user_message.id
    }
  end

  def agent_system_prompt(model_template)
    ModelTemplateService.new(@user).build_system_prompt(model_template)
  end

  def merger_system_prompt(merger_node, agent_nodes)
    origin_prompt = <<~PROMPT
      This agent is used to help users generate outputs from multiple agents.

      You will be given a list of agents and their outputs. Please help users generate the final output based on the agents' outputs.
      The final output of this agent will follow the format of the Reference Output and you process to summarize the outputs will follow closely the Rules.

      Agents:
      <agents>

      Reference Output:
      <reference_output>

      Rules:
      <rules>
    PROMPT

    list_agents = ''
    agent_prompt = ->(agent) { "#{agent.model_template.name}: #{agent.model_template.description} \n" }
    agent_nodes.each { |agent| list_agents += agent_prompt.call(agent) }

    final_prompt = origin_prompt.gsub('<agents>', list_agents)
    final_prompt.gsub('<reference_output>', merger_node.reference_output)
    final_prompt.gsub('<rules>', merger_node.rules)

    final_prompt
  end

  def create_message(query_list, chat)
    model_template_in_ids = query_list.keys
    model_template_ins = ModelTemplateIn.where(id: model_template_in_ids)

    content = model_template_ins.map { |template_in| "#{template_in.name}: #{template_in.description}" }.join("\n")

    Message.create!(
      chat_id: chat.id,
      content: content,
      sender: 'user'
    )
  end
end
