# frozen_string_literal: true

class ModelTemplateVariableService < AppService
  def initialize(user)
    super
    @openai_service = OpenaiService.new(@user)
  end

  def create(params)
    template = ModelTemplate.find_by(id: params[:model_template_id])
    authorize! template_ownership(template)

    organization_id = template.organization_id
    @ragie = ::External::Ragie.new(organization_id)

    model = Model.where(model_template_id: params[:model_template_id]).first
    exist! model.present?, on_error: 'Model not found'

    template_variable = ModelTemplateVariable.create!(params.except(:v2))

    file_url = template_variable.variable_reference_url

    return template_variable if file_url.blank?

    params = {
      url: file_url,
      external_id: "#{template.class.name}-#{template.id}",
      metadata: {
        object_type: 'model_template_variable',
        object_name: 'variable_reference_url'
      }
    }

    response_document = @ragie.create_document_from_url(params)

    OpenaiFile.create!(
      object_id: template_variable.id,
      object_class: template_variable.class.name,
      object_class_column: 'variable_reference_url',
      openai_file_id: response_document['id']
    )

    template_variable
  end

  def update(id, params)
    template_variable = ModelTemplateVariable.find(id)

    template = template_variable.model_template
    authorize! template_ownership(template)

    organization_id = params[:organization_id] || template.organization_id

    Model.find_by!(model_template_id: template.id)

    current_file_url = template_variable.variable_reference_url

    template_variable.update!(params.except(:v2))

    @ragie = ::External::Ragie.new(organization_id)
    latest_file_url = template_variable.variable_reference_url

    return template_variable if latest_file_url == current_file_url

    openai_file = OpenaiFile.find_by(
      object_id: template_variable.id,
      object_class: template_variable.class.name,
      object_class_column: 'variable_reference_url'
    )

    if openai_file.present?
      begin
        # @ragie.delete_document(openai_file.openai_file_id)
        # TODO: move to background job
        openai_file.discard!
      rescue Faraday::ResourceNotFound
        openai_file.discard!
      end
    end

    return template_variable if latest_file_url.blank?

    params = {
      url: latest_file_url,
      external_id: "#{template.class.name}-#{template.id}",
      metadata: {
        object_type: 'model_template_variable',
        object_name: 'variable_reference_url'
      }
    }

    response_file = @ragie.create_document_from_url(params)
    OpenaiFile.create!(
      object_id: template_variable.id,
      object_class: template_variable.class.name,
      object_class_column: 'variable_reference_url',
      openai_file_id: response_file['id']
    )

    template_variable
  end

  def list(query_params)
    model_template_variables = ::ModelTemplateVariables.new

    filter = query_params.slice(
      :model_template_id,
      :search,
      :organization_id
    )

    # Only filter by organization_id if not admin, super_admin_platform, owner_platform and no specific organization_id provided
    unless %w[admin super_admin_platform
              owner_platform].include?(@user.membership.membership_role) && query_params[:organization_id].present?
      filter = filter.merge(
        organization_id: @user.membership&.organization_id
      )
    end

    filtered = model_template_variables.filter(filter)

    OpenStruct.new(
      model_template_variables: filtered
    )
  end

  def destroy(id)
    template_variable = ModelTemplateVariable.find(id)

    template = template_variable.model_template
    authorize! template_ownership(template)

    ActiveRecord::Base.transaction do
      openai_file = OpenaiFile.find_by(
        object_id: template_variable.id,
        object_class: template_variable.class.name,
        object_class_column: 'variable_reference_url'
      )

      if openai_file.present?
        begin
          @ragie = ::External::Ragie.new(template.organization_id)
          # TODO: move to background job
          # @ragie.delete_document(openai_file.openai_file_id)
        rescue Faraday::ResourceNotFound
          {}
        end

        openai_file.discard!
      end
      template_variable.discard!
    end
  end

  private

  def template_ownership(template)
    return true if platform_admin?
    return true if @user.membership&.organization_id == template.organization_id

    false
  end
end
