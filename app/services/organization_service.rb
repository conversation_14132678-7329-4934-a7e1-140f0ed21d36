# frozen_string_literal: true

class OrganizationService < AppService
  def create(params)
    authorize_user_roles!(@user, %w[super_user admin super_admin partner_admin])

    monthly_credits_refresh = params.delete(:monthly_credits_refresh).to_i
    max_members = params.delete(:max_members).to_i
    starting_credits = params.delete(:starting_credits).to_i
    designated_owner_email = params.delete(:designated_owner_email)&.strip&.downcase
    role = params.delete(:role) || 0

    params[:code] = params[:code].to_s.strip.downcase if params[:code].present?
    params[:code] = params[:name].parameterize.downcase if params[:code].blank?
    params[:created_by_id] = @user.id

    exist_code = Organization.where('LOWER(code) = ?', params[:code].downcase).exists?
    assert! !exist_code, on_error: 'Organization code already exists'

    refresh_date = params.delete(:refresh_date) || Time.current.day
    assert! refresh_date.to_i.between?(0, 31), on_error: 'Refresh date invalid'

    plan_threshold = OrganizationsPlansThreshold.new

    ActiveRecord::Base.transaction do
      current_time = Time.current
      organization = Organization.create!(params)
      membership = Membership.find_by(user_id: @user.id, organization_id: current_user_organization)

      exist! membership, on_error: 'User not registered in the organization'

      membership.update!(
        managed_organization_ids: (membership.managed_organization_ids || []).push(organization.id).uniq
      )

      if Membership.list_membership_roles[role.to_i] == 'partner_admin'
        user = User.find_by(email: designated_owner_email)
      end

      plan_threshold = OrganizationsPlansThreshold.create!(
        organization_id: organization.id,
        monthly_credits_refresh: monthly_credits_refresh,
        max_members: max_members,
        max_workspaces: 1,
        purchased_credits: starting_credits,
        refresh_date: refresh_date
      )

      CreditHistory.create!(
        organization_id: organization.id,
        action: 'su_create_org',
        monthly_credits: 0,
        purchased_credits: starting_credits,
        action_at: current_time,
        user_id: @user.id
      )

      if designated_owner_email.present?
        assigned_role = Membership.list_membership_roles[role.to_i]
        user = User.find_by(email: designated_owner_email)

        if user.present?
          authorize! role > @user.membership.role,
                     on_error: 'Cannot invite with role with higher access than your role'

          membership = Membership.create!(
            user_id: user.id,
            organization_id: organization.id,
            role: Membership.role_mappings[assigned_role]
          )

          Mailer::OnboardOrganizationMailerJob.perform_later(membership)
        else
          user_management_service = UserManagementService.new(@user)
          user_management_service.create_invitation(
            email: designated_owner_email,
            role: assigned_role,
            organization_team_id: nil,
            primary_workspace: organization.id,
            managed_workspaces: [organization.id],
            organization_id: organization.id
          )
        end
      end

      OpenStruct.new(
        organization: organization,
        organizations_plans_thresholds: [plan_threshold]
      )
    end
  end

  def update(id, params)
    authorize_user_roles!(@user, %w[super_user owner super_admin partner_admin])

    organization = Organization.find(id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    plan_threshold = OrganizationsPlansThreshold.find_by(organization_id: organization.id)

    params[:code] = params[:code].to_s.strip.downcase if params[:code].present?
    if params[:code].present? && params[:code] != organization.code
      assert! !params[:code].include?(' '), on_error: 'Code cannot contain whitespace'
    else
      params[:code] = organization.code
    end

    exist_code = Organization.where('LOWER(code) = ? AND id != ?', params[:code].downcase, organization.id).exists?
    assert! !exist_code, on_error: 'Organization code already exists'

    refresh_date = params.delete(:refresh_date)
    assert! refresh_date.nil? || (refresh_date.to_i >= 0 && refresh_date.to_i <= 31), on_error: 'Refresh date invalid'

    update_plan_params = {
      monthly_credits_refresh: params.delete(:monthly_credits_refresh),
      max_members: params.delete(:max_members),
      refresh_date: refresh_date
    }.compact

    add_credits = params.delete(:add_credits)
    update_plan_params[:purchased_credits] = plan_threshold.purchased_credits + add_credits.to_i if add_credits.present?

    ActiveRecord::Base.transaction do
      current_time = Time.current

      organization.update!(params)

      if %w[super_user admin super_admin super_admin_platform owner_platform].include?(@user.membership.membership_role)
        plan_threshold&.update(update_plan_params)

        if add_credits.present?
          CreditHistory.create!(
            organization_id: organization.id,
            action: 'su_add_credits',
            monthly_credits: 0,
            purchased_credits: add_credits,
            action_at: current_time,
            user_id: @user.id
          )
        end
      end
    end

    OpenStruct.new(
      organization: organization,
      organizations_plans_thresholds: [plan_threshold]
    )
  end

  def show(id)
    authorize_user_roles!(@user, %w[super_user owner super_admin partner_admin])

    organization = Organization.find(id)

    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    plan_threshold = OrganizationsPlansThreshold.find_by(organization_id: organization.id)

    OpenStruct.new(
      organization: organization,
      organizations_plans_thresholds: [plan_threshold]
    )
  end

  def index(query_params)
    authorize_user_roles!(@user, %w[super_user admin super_admin partner_admin])

    organizations = ::Organizations.new

    filter = query_params.slice(:search, :page, :per_page)

    # Allow admin, super_admin_platform and owner_platform to filter by organization_id
    if (platform_admin? || @user.role == 'partner_admin') && query_params[:organization_id].present?
      filter[:organization_id] = query_params[:organization_id]
    end

    if filter[:organization_id].blank? && @user.role == 'partner_admin'
      filter[:organization_id] = @user.membership.managed_organization_ids
    end

    filter = filter.merge(
      disable_pagination: true
    )

    filtered = organizations.filter(filter)
    org_ids = filtered.pluck(:id)

    OpenStruct.new(
      organizations: filtered,
      organizations_plans_thresholds: OrganizationsPlansThreshold.where(organization_id: org_ids),
      owner_memberships: Membership.includes(:user).where(organization_id: org_ids,
                                                          role: Membership.role_mappings['owner']).order('users.email')
    )
  end

  def transfer_ownership(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner admin super_admin partner_admin])

    if Membership.role_mappings['owner'] == membership.role
      authorize! id.to_i == membership.organization_id, on_error: 'Not owned organization!'
    end

    email = params.delete(:designated_owner_email)&.strip&.downcase

    ActiveRecord::Base.transaction do
      Membership.where(organization_id: id, role: Membership.role_mappings['owner'])
                .update_all(role: Membership.role_mappings['super_admin'])

      if email.present?
        new_user = User.find_by(email: email)

        if new_user.present?
          new_membership = Membership.unscoped.find_by(user_id: new_user.id)
          new_membership.update(
            role: Membership.role_mappings['owner'],
            organization_id: id,
            discarded_at: nil,
            organization_team_ids: []
          )

          Mailer::OnboardOrganizationMailerJob.perform_later(new_membership)
        else
          new_user = User.create!(email: email, password: "#{id}1234xzq3", display_name: email)

          new_membership = Membership.create!(
            role: Membership.role_mappings['owner'],
            organization_id: id,
            user_id: new_user.id
          )

          Mailer::OnboardOrganizationMailerJob.perform_later(new_membership)
        end
      end
    end
  end

  def get_remaining_tokens(id)
    authorize_user_roles!(@user, %w[admin partner_admin])

    organization = Organization.find(id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    plan_threshold = OrganizationsPlansThreshold.find_by(organization_id: organization.id)

    OpenStruct.new(
      id: organization.id,
      name: organization.name,
      purchased_credits: plan_threshold&.purchased_credits.to_f,
      remaining_monthly_credits: plan_threshold&.remaining_monthly_credits.to_f,
      monthly_credits_refresh: plan_threshold&.monthly_credits_refresh,
      refresh_date: plan_threshold&.refresh_date
    )
  end

  def destroy(id)
    authorize_user_roles!(@user, %w[admin super_admin partner_admin])

    organization = Organization.find(id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'
    ActiveRecord::Base.transaction do
      organization.discard!
    end
  end
end
