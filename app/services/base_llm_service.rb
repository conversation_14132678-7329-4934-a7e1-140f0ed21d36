# frozen_string_literal: true

require 'tiktoken_ruby'

class BaseLlmService < AppService
  def initialize(user)
    super

    @client = initialize_client
    @additional_context = ''
  end

  def initialize_chat_v2(params)
    params[:chat_type] ||= 'general'
    user_prompt = params[:query].to_s
    chat_id = params[:chat_id]
    file_url = params[:file_url].to_s
    image_url = params[:image_url].to_s
    source_model_template_id = params[:source_model_template_id]
    query_list = params[:query_list]
    @web_search = params[:web_search]
    @organization_id = params[:organization_id]

    test_prompt_params = as_boolean(params[:test_prompt])
    regenerate_chat = as_boolean(params[:regenerate])
    test_prompt = source_model_template_id.present? && test_prompt_params

    authorize! test_prompt, on_error: 'Invalid Prompt' if test_prompt_params

    organization_id = params[:organization_id] if @user.platform_admin? || @user.role == 'partner_admin'
    organization_id ||= @user.membership.organization_id

    chat = Chat.find_by_id(chat_id)
    chat ||= ChatService.new(@user).create(params)
    @chat = chat

    @ragie = ::External::Ragie.new(organization_id)

    source_template = ModelTemplate.find_by_id(chat.source_model_template_id)

    handle_test_prompt(test_prompt, source_template.id, chat) if test_prompt

    validate_credits(chat, user_prompt, organization_id)

    message_data = initialize_message_data(chat)
    user_messages = {}

    if regenerate_chat
      handle_regeneration(chat, message_data)
      user_prompt = 'Refine your answer'
    end

    if source_template.present? && query_list.present?
      handle_multi_input(chat, query_list, message_data, user_messages)
    elsif query_list.present?
      handle_single_input(query_list, message_data, user_messages)
    end

    message_type = determine_message_type(file_url, image_url)
    handle_message_type(message_type, file_url, image_url, user_prompt, message_data, user_messages)

    if source_template&.template_type == 'workflow'
      workflow_id = AgentWorkflowNode.find_by(model_template_id: source_template.id)&.agent_workflow_id
    end

    create_messages(message_data, user_messages, chat, workflow_id)
  end

  def update_remaining_tokens_v2(params)
    return unless params

    organization = Organization.find_by_id(params[:organization_id])
    organization ||= @user.membership.organization

    org_plan = organization.organizations_plans_thresholds.first
    purchased_credits = org_plan.purchased_credits
    monthly_credits = org_plan.remaining_monthly_credits
    prev_purchased_credits = purchased_credits
    prev_monthly_credits = monthly_credits

    assistant_message = params.assistant_message_obj
    user_message = params.user_message_obj

    credits_used = (assistant_message.credits_used || 0) + (user_message.credits_used || 0)

    # Deduct from monthly credits first until 0
    # then from purchased credits if not enough
    # then again from monthly credits until negative if it still not enough
    monthly_deducted = false
    if monthly_credits > 0
      monthly_credits -= credits_used
      monthly_deducted = true
    end

    if monthly_credits <= 0
      credits_used_left = 0
      if monthly_deducted
        credits_used_left = monthly_credits.abs
        monthly_credits = 0
      else
        credits_used_left = credits_used
      end

      purchased_credits -= credits_used_left

      if purchased_credits < 0
        monthly_credits += purchased_credits
        purchased_credits = 0
      end
    end

    CreditHistory.create!(
      organization_id: organization.id,
      action: 'org_chat_stream',
      monthly_credits: -(prev_monthly_credits - monthly_credits),
      purchased_credits: -(prev_purchased_credits - purchased_credits),
      action_at: Time.current,
      user_id: @user.id,
      message_ids: [assistant_message&.id, user_message&.id].compact
    )

    org_plan.update!(
      purchased_credits: purchased_credits,
      remaining_monthly_credits: monthly_credits
    )
  end

  private

  def initialize_client
    raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"
  end

  def handle_test_prompt(_test_prompt, source_model_template_id, chat)
    template = ModelTemplate.find(source_model_template_id)
    template.update(test_prompt_chat_id: chat.id)
  end

  def validate_credits(chat, user_prompt, organization_id)
    org_credits = remaining_tokens_v2(organization_id)
    authorize! org_credits.positive?, on_error: 'Token insufficient, contact admin to topup token!'

    tokens = token_count_v2(user_prompt)
    model = chat.model
    total_tokens = token_count_v2(model.instruction) + tokens
    total_credits = token_to_credit(model.model, total_tokens, 'user')

    authorize! total_credits <= org_credits, on_error: 'Token insufficient, contact admin to topup token!'
    authorize! total_credits <= model.max_tokens.to_i,
               on_error: 'Prompt messages are exceeding the maximum allowed tokens'
  end

  def initialize_message_data(chat)
    {
      chat_id: chat.id,
      sender: 'user',
      content: '',
      model_used: chat.model.model
    }
  end

  def handle_regeneration(chat, message_data)
    # TODO: save openai message id, then when regenerate
    # discard openai message from related thread
    # reason: to conserve token usage
    last_user_message = Message.where(chat_id: chat.id, sender: 'user',
                                      status_on_thread: 'present').order(:created_at).last
    last_assistant_message = Message.where(chat_id: chat.id, sender: 'assistant',
                                           status_on_thread: 'present').order(:created_at).last

    authorize! last_user_message.present?, on_error: 'There is no chat history'
    last_assistant_message.update(status_on_thread: 'removed')
    message_data[:status_on_thread] = 'hidden'
  end

  def create_messages(message_data, user_messages, chat, workflow_id)
    openai_file_ids = message_data.delete(:openai_file_ids)
    user_message_obj = ::Message.create!(message_data)

    handle_file_ids(openai_file_ids, user_message_obj)

    assistant_message_obj = ::Message.new(
      chat_id: chat.id,
      sender: 'assistant',
      content: '',
      model_used: chat.model.model
    )

    OpenStruct.new(
      chat: chat,
      model: chat.model,
      user_messages: user_messages,
      assistant_message_obj: assistant_message_obj,
      user_message_obj: user_message_obj,
      save_convo: true,
      prompt_messages: [user_messages],
      web_search: @web_search,
      organization_id: @organization_id,
      workflow_id: workflow_id
    )
  end

  def handle_file_ids(openai_file_ids, user_message_obj)
    return unless openai_file_ids.present?

    openai_file_ids.each do |openai_file_id|
      next unless openai_file_id.present?

      OpenaiFile.create!(
        object_id: user_message_obj.id,
        object_class: user_message_obj.class.name,
        object_class_column: 'file_url',
        openai_file_id: openai_file_id
      )
    end
  end

  def remaining_tokens_v2(organization_id)
    organization = Organization.find(organization_id)
    org_plan = organization.organizations_plans_thresholds.first
    org_plan.purchased_credits + org_plan.remaining_monthly_credits
  end

  def token_count_v2(text)
    OpenAI.rough_token_count(text)
  end

  def remove_hex_identifier(filename)
    arr_filename = filename.split('.')

    if arr_filename.size > 1
      no_ext_filename = arr_filename[..(arr_filename.size - 2)].join('.')
      ext = arr_filename.last

      arr_no_ext_filename = no_ext_filename.split('-')
      if arr_no_ext_filename.size > 1
        no_hex_filename = arr_no_ext_filename[..(arr_no_ext_filename.size - 2)].join('-')
        filename = [no_hex_filename, ext].join('.')
      end
    end

    filename
  end

  def handle_multi_input(chat, query_list, message_data, user_messages)
    begin
      query_list = JSON.parse(query_list)
    rescue JSON::ParserError
      authorize! false, on_error: 'Input invalid'
    rescue TypeError
      true
    end

    authorize! !query_list.blank?, on_error: 'Input invalid'

    input_ids = query_list.keys
    input_req = ::ModelTemplateIn.where(id: input_ids)
    authorize! input_req.count == input_ids.size, on_error: 'Not all Input filled'

    message_file_ids = []
    file_urls = []
    image_urls = []
    user_prompt = ''
    message_data_content = []
    file_metadata = []

    input_ids.each do |input_id|
      data_input = query_list[input_id]
      query_input = data_input['query']
      file_input = data_input['file_url']
      image_input = data_input['image_url']

      authorize! !query_input.blank?, on_error: 'Input invalid'

      curr_in = input_req.find { |i| i.id == input_id.to_i }
      curr_message_data_content = "**#{curr_in.name}**:\n\n#{query_input}"

      if image_input.present?
        image_urls << image_input

        user_prompt += ", supplement image: '#{image_input}'"
        curr_message_data_content += ", '#{image_input}'"
      end

      if file_input.present?
        file_urls << file_input
        file_id = process_file(file_input, chat.id)

        message_file_ids << file_id
        file_metadata << { id: file_id, query: query_input }
        user_prompt += ", supplement file: '#{message_file_ids}'"

        uri = URI.parse(file_input)
        filename = File.basename(uri.path)
        curr_message_data_content += ", '#{remove_hex_identifier(filename)}'"
      end

      user_prompt += ";\n"
      message_data_content << curr_message_data_content
    end

    content = [
      { type: 'text', text: user_prompt }
    ]

    image_urls.each do |image_url|
      content << { type: 'image_url', image_url: { url: image_url } }
    end

    attachments = file_metadata.map do |metadata|
      @additional_context += retrieve_content([metadata[:id]], metadata[:query])

      { file_id: metadata[:id], tools: [{ type: 'file_search' }] }
    end

    variable_ids = ModelTemplateVariable.where(model_template_id: chat.source_model_template_id)
    reference_ids = ::OpenaiFile.where(object_id: chat.source_model_template_id,
                                       object_class: 'ModelTemplate').pluck(:openai_file_id)
    reference_ids += ::OpenaiFile.where(object_id: variable_ids,
                                        object_class: 'ModelTemplateVariable').pluck(:openai_file_id)
    @additional_context += retrieve_content(reference_ids, user_prompt)

    user_messages.merge!(
      role: 'user',
      content: content,
      attachments: attachments
    )

    message_data[:content] = message_data_content.join("\n\n")
    message_data[:image_url] = image_urls.join(' ')
    message_data[:file_url] = file_urls.join(' ')
    message_data[:openai_file_ids] = message_file_ids
  end

  def handle_single_input(query_list, message_data, user_messages)
    begin
      query_list = JSON.parse(query_list)
    rescue JSON::ParserError
      authorize! false, on_error: 'Input invalid'
    rescue TypeError
      true
    end

    authorize! !query_list.blank?, on_error: 'Input invalid'

    input_id = query_list.keys.first
    data_input = query_list[input_id]

    user_prompt = data_input['query']
    file_url = data_input['file_url']
    image_url = data_input['image_url']

    message_type = determine_message_type(file_url, image_url)
    handle_message_type(message_type, file_url, image_url, user_prompt, message_data, user_messages)
  end

  def determine_message_type(file_url, image_url)
    if file_url.present? && image_url.present?
      'image_and_file'
    elsif file_url.present?
      'file_search'
    elsif image_url.present?
      'image'
    else
      'general'
    end
  end

  def handle_message_type(message_type, file_url, image_url, user_prompt, message_data, user_messages)
    case message_type
    when 'image_and_file'
      handle_image_and_file(file_url, image_url, user_prompt, message_data, user_messages)
    when 'file_search'
      handle_file_search(file_url, user_prompt, message_data, user_messages)
    when 'image'
      handle_image(image_url, user_prompt, message_data, user_messages)
    when 'general'
      handle_general(user_prompt, message_data, user_messages)
    end
  end

  def handle_image_and_file(file_url, image_url, user_prompt, message_data, user_messages)
    file_id = process_file(file_url)
    @additional_context += retrieve_content([file_id], user_prompt)

    user_messages.merge!(
      role: 'user',
      content: [
        { type: 'text', text: user_prompt },
        { type: 'image_url', image_url: { url: image_url } }
      ]
    )

    message_data[:content] = user_prompt
    message_data[:image_url] = image_url
    message_data[:file_url] = file_url
  end

  def handle_image(image_url, user_prompt, message_data, user_messages)
    user_messages.merge!(
      role: 'user',
      content: [
        { type: 'text', text: user_prompt },
        { type: 'image_url', image_url: { url: image_url } }
      ]
    )

    message_data[:content] = user_prompt
    message_data[:image_url] = image_url
  end

  def handle_general(user_prompt, message_data, user_messages)
    return if user_prompt.blank?

    user_messages.merge!(
      role: 'user',
      content: user_prompt
    )

    message_data[:content] = user_prompt
  end

  def handle_file_search(file_url, user_prompt, message_data, user_messages)
    file_id = process_file(file_url)
    @additional_context += retrieve_content([file_id], user_prompt)

    user_messages.merge!(
      role: 'user',
      content: user_prompt
    )

    message_data[:content] = user_prompt
    message_data[:file_url] = file_url
  end

  def process_file(file_url, chat_id = @chat.id)
    params = {
      url: file_url,
      external_id: "Chat-#{chat_id}"
    }

    create_response = @ragie.create_document_from_url(params)
    create_response['id']
  end

  def status_document(document_id)
    response = @ragie.get_document(document_id)
    response['status']
  end

  def retrieve_content(document_ids, query)
    document_ids.each do |document_id|
      loop do
        status = status_document(document_id)

        break if status == 'failed'

        break if status == 'ready'

        sleep 1
      end
    end

    responses = @ragie.retrieve(query, document_ids: document_ids, top_k: 10)
    responses['scored_chunks'].map do |chunk|
      chunk['text']
    end.join("\n")
  end
end
