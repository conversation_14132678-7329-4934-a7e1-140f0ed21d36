# frozen_string_literal: true

class UserService < AppService
  def see(id)
    authorize! can_see? id

    User.find(id)
  end

  def get_by_email(email)
    authorize_user_roles!(@user, %w[super_user admin super_admin_platform owner_platform])

    user = User.find_by(email: email)
    exist! user

    user
  end

  def update(params)
    if params[:password]
      assert! @user.authenticate(params[:current_password]), on_error: 'Invalid current password. Try again'
    end

    @user.update!(params.except(:current_password))
    @user
  end

  def change_password(user_id, params)
    target_user = User.find(user_id)
    new_password = params[:new_password]

    err_msg = 'You do not have permission to change this user password'
    authorize! platform_admin?, on_error: err_msg

    highest_target_role = Membership.where(user_id: target_user.id).order(role: :asc)&.first&.role || 99
    highest_current_role = Membership.where(user_id: @user.id).order(role: :asc)&.first&.role || 99

    authorize! highest_current_role < highest_target_role, on_error: err_msg if target_user.id != @user.id

    target_user.update!(password: new_password)
  end

  private

  def can_see?(id)
    @user.id.to_s == id.to_s
  end
end
