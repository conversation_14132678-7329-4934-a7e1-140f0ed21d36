# frozen_string_literal: true

class PromptEvalService < AppService
  def initialize(user)
    super
    @prompt_evals = PromptEvals.new
  end

  def index(params)
    prompt_evals = @prompt_evals.filter(params)

    OpenStruct.new(prompt_evals: prompt_evals)
  end

  def show(id)
    prompt_eval = PromptEval.find(id)

    prompt_eval_results = PromptEvalResult.where(prompt_eval_id: id).pluck(:status).uniq

    status = 'completed'
    status = 'pending' if prompt_eval_results.include?('pending')
    status = 'failed' if prompt_eval_results.include?('failed')

    OpenStruct.new(prompt_eval: prompt_eval, status: status)
  end

  def evaluate(params)
    params[:user_id] = @user.id
    model_bank_ids = params.delete(:model_bank_ids)

    assert! model_bank_ids.present?, on_error: 'You need to select at least one model'
    assert! params[:prompt].present?, on_error: 'Prompt is required'
    assert! params[:params].present?, on_error: 'Params are required'
    assert! params[:params].is_a?(Hash), on_error: 'Params must be a hash'

    model_banks = ModelBank.where(id: model_bank_ids)
    request_raw = initialize_request_params(params)

    ActiveRecord::Base.transaction do
      prompt_eval = PromptEval.create!(params.merge(request_raw: request_raw))

      import_records = model_banks.map do |model_bank|
        PromptEvalResult.new(
          prompt_eval_id: prompt_eval.id,
          model_bank_id: model_bank.id,
          status: 'pending',
          result_text: ''
        )
      end

      result_ids = PromptEvalResult.import(import_records).ids

      result_ids.each do |result_id|
        RunEvalModelJob.perform_later(result_id)
      end

      prompt_eval
    end
  end

  private

  def initialize_request_params(params)
    contents = ''
    temperature = params.dig(:params, :temperature) || 1
    clean_input = params[:params].except(:temperature)

    clean_input.map do |key, value|
      contents += "#{key}: #{value}\n"
    end

    user_messages = [
      {
        role: 'system',
        content: params[:prompt]
      },
      {
        role: 'user',
        content: contents
      }
    ]

    {
      temperature: temperature,
      messages: user_messages
    }
  end
end
