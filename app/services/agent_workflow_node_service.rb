# frozen_string_literal: true

class AgentWorkflowNodeService < AppService
  def initialize(user)
    super

    @agent_workflow = nil
    @organization = nil
    @model_bank = nil
    @model_template = nil
    @knowledge_base_file = nil
  end

  def create(params)
    validate_params!(params)

    AgentWorkflowNode.transaction do
      params[:model_template_id] = @model_template.id if @model_template.present?
      AgentWorkflowNode.create!(params)
    end
  end

  def update(id, params)
    agent_workflow_node = AgentWorkflowNode.find(id)
    params[:agent_workflow_id] = agent_workflow_node.agent_workflow_id

    validate_params!(params)

    agent_workflow_node.update!(params)
    agent_workflow_node
  end

  private

  def validate_params!(params)
    @agent_workflow = AgentWorkflow.find(params[:agent_workflow_id])
    @organization = @agent_workflow.organization
    @model_bank = ModelBank.find(params[:model_bank_id]) if params[:model_bank_id].present?
    @model_template = ModelTemplate.find(params[:model_template_id]) if params[:model_template_id].present?

    return unless params[:knowledge_base_file_id].present?

    @knowledge_base_file = KnowledgeBaseFile.find(params[:knowledge_base_file_id])
  end

  def process_merger_creation_node!(params)
    return if params[:workflow_type] != 'merger'

    model_template_creation_params = {
      name: @agent_workflow.name,
      organization_id: @organization.id,
      model: @model_bank.model,
      prompt: params[:rules],
      placeholder: params[:reference_output],
      user: @user.id
    }

    @model_template = ModelTemplate.create!(model_template_creation_params)

    agent_ids = AgentWorkflowNode.where(agent_workflow_id: @agent_workflow.id,
                                        workflow_type: 'agent').pluck(:model_template_id)
    agent_inputs = ModelTemplateInput.where(model_template_id: agent_ids)

    records = agent_inputs.map do |agent_input|
      dup_agent = agent_input.dup
      dup_agent.model_template_id = @model_template.id
      dup_agent
    end

    ModelTemplateIn.import records
  end
end
