# frozen_string_literal: true

class TemplateCategoryService < AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin super_admin_platform owner_platform])

    # Allow admin, super_admin_platform and owner_platform to specify organization_id, otherwise use current user's organization

    params[:validated] = true
    params[:general_category] = false
    params[:organization_id] = if platform_admin? && params[:organization_id].present?
                                 params[:organization_id]
                               else
                                 @user.membership&.organization_id
                               end

    ActiveRecord::Base.transaction do
      TemplateCategory.create!(params)
    end
  end

  def update(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin super_admin_platform owner_platform])

    params = params.slice(:name)

    category = TemplateCategory.find(id)
    authorize! category_ownership(membership, category)

    ActiveRecord::Base.transaction do
      category.update(params)
    end

    category
  end

  def show(id)
    membership = verify_user_organization(@user)

    category = TemplateCategory.find(id)
    authorize! category_ownership(membership, category)

    category
  end

  def index(query_params)
    verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin super_admin_platform owner_platform])

    categories = ::TemplateCategories.new

    filter = query_params.slice(:search, :page, :per_page, :disable_pagination)

    if platform_admin?
      filter[:organization_id] = query_params[:organization_id] if query_params[:organization_id].present?
    else
      filter[:organization_id] = @user.membership.organization_id
    end

    filtered_categories = categories.filter(filter)

    # Count templates for each category
    used_template_count = ModelTemplate.where(template_category_id: filtered_categories.pluck(:id))
                                       .group(:template_category_id)
                                       .count

    OpenStruct.new(
      template_categories: filtered_categories,
      used_template_count: used_template_count
    )
  end

  def destroy(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin super_admin_platform owner_platform])

    category = TemplateCategory.find(id)
    authorize! category_ownership(membership, category)

    new_template_category_id = params.delete(:new_template_category_id)

    ActiveRecord::Base.transaction do
      if new_template_category_id.present?
        new_category = TemplateCategory.find(id)
        authorize! category_ownership(membership, new_category)

        # change model template template category id
        ModelTemplate.where(template_category_id: id)
                     .update_all(template_category_id: new_template_category_id)
      else
        ModelTemplate.where(template_category_id: id)
                     .update_all(template_category_id: nil)
      end

      category.discard!
    end
  end

  private

  def category_ownership(membership, category)
    return true if platform_admin?

    membership.organization_id == category&.organization_id
  end
end
