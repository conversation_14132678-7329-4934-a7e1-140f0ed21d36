# frozen_string_literal: true

class ChatService < AppService
  def create(params)
    verify_user_organization(@user)

    source_model_template_id = params[:source_model_template_id]
    user_workspace_result = user_workspace_id(params[:workspace_id])

    params[:workspace_id] = user_workspace_result[:workspace_id]
    workspaces_membership = user_workspace_result[:workspaces_membership]

    params[:model] = 'openai/gpt-4o-mini' unless params[:model].present?
    params[:name] = params[:query] unless params[:name].present?

    ActiveRecord::Base.transaction do
      if source_model_template_id.present?
        model = Model.find_by(model_template_id: source_model_template_id, model: params[:model])

        if model.blank?
          other_model = Model.find_by(model_template_id: source_model_template_id)
          model = Model.create!(**other_model.attributes.compact.slice(
            'max_tokens', 'temperature', 'instruction', 'model_template_id'
          ), model: params[:model], name: "#{other_model.model_template.name} #{params[:model]}")
        end
      else
        model = Model.find_by(model: params[:model], model_template_id: nil)
        model ||= Model.create!(
          model: params[:model],
          name: "Chat on #{params[:model]}",
          max_tokens: 16_000,
          temperature: 0.8,
          instruction: 'You are an AI assistant, answer as helpful as possible!'
        )
      end

      chat = Chat.create!(
        workspace_id: params[:workspace_id],
        model_id: model.id,
        name: params[:name],
        chat_type: params[:chat_type] || 'general',
        workspace_membership_membership_id: workspaces_membership.membership_id,
        workspace_membership_workspace_id: workspaces_membership.workspace_id,
        source_model_template_id: source_model_template_id
      )

      test_prompt = as_boolean(params[:test_prompt])

      if source_model_template_id.present? && test_prompt
        template = ModelTemplate.find(source_model_template_id)
        template.update!(test_prompt_chat_id: chat.id)
      end

      chat
    end
  end

  def list(params)
    chats = ::Chats.new

    filter = params.slice(:chat_type, :search, :test_prompt).merge(
      disable_pagination: true,
      ownership: @user&.id
    )

    {
      chats: chats.filter(filter),
      workspace_id: params[:workspace_id]
    }
  end

  def show(id)
    workspace_id = user_workspace_id(id)[:workspace_id]

    membership_id = @user.membership.id

    workspaces_membership_ids = WorkspacesMembership.where(membership_id: membership_id).pluck(:workspace_id)
    Workspace.where(id: workspaces_membership_ids).find(workspace_id)
  end

  def update(chat_id, params)
    membership = verify_user_organization(@user)

    chat = ::Chat.find(chat_id)

    wm = WorkspacesMembership.find_by(workspace_id: chat.workspace_membership_workspace_id,
                                      membership_id: chat.workspace_membership_membership_id)
    authorize! wm&.membership&.user_id == membership.user_id

    if params[:model].present?
      model_string = params.delete(:model)
      model = chat.model

      if model&.model_template_id&.present?
        new_model = Model.find_by(model_template_id: model.model_template_id, model: model_string)
        new_model ||= Model.create!(**model.attributes.compact.slice(
          'max_tokens', 'temperature', 'instruction', 'model_template_id'
        ), name: "#{model.model_template.name} #{model_string}", model: model_string)
      end

      new_model ||= Model.find_by(model: model_string, name: model_string)
      params[:model_id] = new_model.id
    end

    chat.update!(params.compact)
    chat.reload
  end

  private

  def user_workspace_id(id)
    membership = @user.membership
    organization_id = membership.organization_id
    workspace = Workspace.where(organization_id: organization_id).first
    if workspace.nil?
      organization = Organization.find_by(id: organization_id)
      workspace = Workspace.create!(organization_id: organization_id, name: organization.name)
    end

    id = workspace.id
    workspaces_memberships = WorkspacesMembership.where(membership_id: membership.id)
    workspaces_membership_ids = workspaces_memberships.pluck(:workspace_id)

    if workspaces_membership_ids.size.positive?
      return {
        workspace_id: id,
        workspaces_membership: workspaces_memberships.first
      }
    end
    workspaces_membership = WorkspacesMembership.create!(workspace_id: workspace.id, membership_id: membership.id,
                                                         role: 'admin')

    {
      workspace_id: id,
      workspaces_membership: workspaces_membership
    }
  end
end
