# frozen_string_literal: true

class MembershipService < AppService
  def initialize(user, current_org_id, selected_org_id)
    @user = user
    @current_org_id = current_org_id
    @selected_org_id = selected_org_id
    super
  end

  def index(query_params)
    membership = verify_user_organization(@user)

    memberships = ::Memberships.new

    filter = query_params.slice(
      :role, :organization_team_id, :name, :page, :per_page, :organization_id
    )

    # Allow admin to access all organizations
    unless platform_admin? && query_params[:organization_id].present?
      filter = filter.merge(
        organization_id: membership.organization_id
      )
    end

    if filter[:organization_team_id].present?
      # For admin role, verify team exists in the target organization
      org_team = if platform_admin?
                   OrganizationTeam.find_by(id: filter[:organization_team_id])
                 else
                   OrganizationTeam.find_by(id: filter[:organization_team_id],
                                            organization_id: membership.organization_id)
                 end
      assert! org_team.present?, on_error: 'Team Invalid'
    end

    if filter[:role].present?
      if filter[:role].is_a?(Array)
        roles_str = filter[:role]
      else
        begin
          roles_str = JSON.parse(filter[:role])
        rescue JSON::ParserError
          raise Invalid, 'Role(s) Format Invalid'
        end
      end

      roles_int = roles_str.map { |r| Membership.role_mappings[r.downcase] }.compact
      filter[:role] = roles_int
    end

    # Exclude platform admins (roles with negative values)
    filter[:exclude_platform_admins] = true unless platform_admin? || @user.role == 'partner_admin'

    filtered = memberships.filter(filter)

    OpenStruct.new(
      memberships: filtered,
      users: User.where(id: filtered.pluck(:user_id).compact),
      organization_teams: OrganizationTeam.where(id: filtered.pluck(:organization_team_ids).union.flatten.uniq.compact)
    )
  end

  def update(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin])

    to_be_updated_membership = Membership.find(id)
    # For admin role, check if organization_id is provided in params
    unless platform_admin?
      authorize! to_be_updated_membership.organization_id == membership.organization_id,
                 on_error: 'User Invalid'
    end

    if platform_admin?
      authorize! to_be_updated_membership.role >= membership.role,
                 on_error: 'Cannot update with role higher access than your role'
    else
      # Only check role hierarchy if not admin
      authorize! to_be_updated_membership.role > membership.role,
                 on_error: 'Cannot update with role higher access than your role'
    end

    organization_team_ids = params.delete(:organization_team_ids)
    assert! !organization_team_ids.nil?, on_error: 'Input Invalid'

    new_role = params.delete(:role)
    new_display_name = params.delete(:name)

    if to_be_updated_membership.role == Membership.role_mappings['owner_platform']
      owner_platform_membership = Membership.find_by(role: Membership.role_mappings['owner_platform'])

      if owner_platform_membership.present? && to_be_updated_membership.id == owner_platform_membership.id && to_be_updated_membership.role != Membership.role_mappings['owner_platform']
        raise Invalid, 'Cannot Remove Owner Platform Role'
      end
    end

    if new_role.present?
      role_number = Membership.role_mappings[new_role]
      assert! role_number.present?, on_error: 'Role not found'
    end

    unless organization_team_ids.is_a?(Array)
      begin
        organization_team_ids = JSON.parse(organization_team_ids)
      rescue JSON::ParserError
        raise Invalid, 'Organization Team IDs Format Invalid'
      end
    end

    assert! !organization_team_ids.nil? && organization_team_ids.is_a?(Array), on_error: 'Input Invalid'

    # For admin role, verify teams exist in the target organization
    input_org_team = if platform_admin? && params[:organization_id].present?
                       OrganizationTeam.where(id: organization_team_ids,
                                              organization_id: params[:organization_id].to_i).count
                     else
                       OrganizationTeam.where(id: organization_team_ids,
                                              organization_id: membership.organization_id).count
                     end
    assert! input_org_team == organization_team_ids.size, on_error: 'Team Invalid'

    ActiveRecord::Base.transaction do
      organization_team_ids = organization_team_ids.compact.uniq.sort
      to_be_updated_membership.organization_team_ids = organization_team_ids

      to_be_updated_membership.role = role_number if new_role.present?

      if new_role === 'partner_admin'
        primary_org = params[:primary_workspace]
        managed_org = params[:managed_workspaces]
        to_be_updated_membership.managed_organization_ids = [primary_org, *managed_org].compact.uniq
      end

      if Membership.role_mappings['owner_platform'] == membership.role && new_role == 'owner_platform'
        Membership.find(membership.id).update(role: Membership.role_mappings['super_admin_platform'])
      end

      if new_display_name.present?
        to_be_updated_user = to_be_updated_membership.user
        to_be_updated_user.update(display_name: new_display_name)
      end

      to_be_updated_membership.save!
    end

    to_be_updated_membership
  end

  def destroy(id, _params = {})
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin])

    to_be_deleted_membership = Membership.find(id)

    unless platform_admin?
      authorize! to_be_deleted_membership.organization_id == membership.organization_id,
                 on_error: 'User Invalid'
    end

    # Only check role hierarchy if not admin

    authorize! to_be_deleted_membership.role > membership.role,
               on_error: 'Cannot delete with role higher access than your role'

    ActiveRecord::Base.transaction do
      to_be_deleted_membership.discard!
    end
  end

  def organization_id
    @user.membership.organization_id
  end
end
