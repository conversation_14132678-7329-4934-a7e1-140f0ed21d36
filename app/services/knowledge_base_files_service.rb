# frozen_string_literal: true

class KnowledgeBaseFilesService < AppService
  def initialize(organization)
    @organization = organization
  end

  def create(params)
    authorize_user_roles!(Current.user, %w[owner super_admin team_admin partner_admin])

    # Determine which organization to use
    target_organization = if params[:organization_id].present?
                            Organization.find(params[:organization_id])
                          else
                            @organization
                          end

    # Check plan limits before creating
    check_plan_limits!(target_organization)

    knowledge_base = KnowledgeBaseFile.new(params.merge(organization: target_organization))

    raise Invalid, knowledge_base.errors.full_messages.join(', ') unless knowledge_base.save

    # For OpenRouter, we don't need to upload to OpenAI since files are handled differently
    # The file_url will be used directly in chat completion

    knowledge_base
  end

  def update(id, params)
    knowledge_base = KnowledgeBaseFile.find(id)
    authorize_knowledge_base_access!(knowledge_base)
    authorize_user_roles!(Current.user, %w[owner super_admin team_admin])

    raise Invalid, knowledge_base.errors.full_messages.join(', ') unless knowledge_base.update(params)

    knowledge_base
  end

  def destroy(id)
    knowledge_base = KnowledgeBaseFile.find(id)
    authorize_knowledge_base_access!(knowledge_base)
    authorize_user_roles!(Current.user, %w[owner super_admin team_admin])

    knowledge_base.discard!
    knowledge_base
  end

  def list(query_params)
    knowledge_bases = ::KnowledgeBaseFiles.new

    filter = query_params.slice(
      :search,
      :file_type,
      :active_only,
      :organization_id
    )

    # For platform admins and partner admins, allow filtering by organization_id
    unless can_access_multiple_organizations? && query_params[:organization_id].present?
      filter = filter.merge(
        organization_id: @organization.id
      )
    end

    filtered = knowledge_bases.filter(filter)

    OpenStruct.new(
      knowledge_bases: filtered
    )
  end

  def show(id)
    knowledge_base = KnowledgeBaseFile.find(id)
    authorize_knowledge_base_access!(knowledge_base)

    knowledge_base
  end

  def get_organization_knowledge_base_files
    KnowledgeBaseFile.active.by_organization(@organization.id)
  end

  # Get plan limits for knowledge base files
  def get_plan_limits
    plan_threshold = @organization.organizations_plans_thresholds.first

    result = OpenStruct.new(
      max_files: 0,
      current_files: 0,
      remaining_files: 0
    )

    return result unless plan_threshold

    current_files = KnowledgeBaseFile.active.by_organization(@organization.id).count
    max_files = plan_threshold.max_knowledge_base_files || 0
    remaining_files = [max_files - current_files, 0].max

    result.max_files = max_files
    result.current_files = current_files
    result.remaining_files = remaining_files

    result
  end

  def check_plan_limits!(organization)
    plan_threshold = organization.organizations_plans_thresholds.first
    return unless plan_threshold

    current_files = KnowledgeBaseFile.active.by_organization(organization.id).count
    max_files = plan_threshold.max_knowledge_base_files || 0
    remaining_files = [max_files - current_files, 0].max

    return unless remaining_files <= 0

    raise Invalid, "Plan limit exceeded. Maximum #{max_files} knowledge base files allowed. Current: #{current_files}"
  end

  private

  def authorize_knowledge_base_access!(knowledge_base)
    # Platform admins can access any knowledge base
    return if platform_admin?

    # Partner admins can access knowledge bases in organizations they created
    if Current.user.role == 'partner_admin'
      user_id = Current.user.id.is_a?(Integer) ? Current.user.id.to_s : Current.user.id
      return if knowledge_base.organization.created_by_id == user_id
    end

    # Regular users can only access knowledge bases in their own organization
    authorize! knowledge_base.organization_id == @organization.id, on_error: 'Not authorized'
  end

  def can_access_multiple_organizations?
    platform_admin? || Current.user.role == 'partner_admin'
  end

  def platform_admin?
    return false unless Current.user&.membership

    %w[admin super_admin_platform owner_platform].include?(Current.user.membership.membership_role)
  end
end
