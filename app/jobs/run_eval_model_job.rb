# frozen_string_literal: true

class RunEvalModelJob < ApplicationJob
  queue_as :default

  def perform(prompt_eval_result_id)
    prompt_eval_result = PromptEvalResult.find(prompt_eval_result_id)
    prompt_eval = prompt_eval_result.prompt_eval
    request_raw = prompt_eval.request_raw.deep_symbolize_keys
    model_bank = prompt_eval_result.model_bank

    response_text = ''
    response_raw = []

    request_raw[:model] = model_bank.code
    request_raw[:stream] = proc do |chunk, _bytesize|
      response_text += chunk.dig('choices', 0, 'delta', 'content').to_s
      response_raw << chunk.dig('choices', 0, 'delta')
    end

    prompt_eval_result.update!(status: 'running')
    client.chat(parameters: request_raw)

    prompt_eval_result.update!(status: 'completed', result_text: response_text, response_raw: { raw: response_raw })
  rescue ActiveRecord::RecordNotFound => e
    raise e
  rescue StandardError => e
    prompt_eval_result.mark_as_failed!(e.message)
  end

  private

  def client
    OpenAI::Client.new(
      access_token: Rails.application.credentials.openrouter_key,
      uri_base: 'https://openrouter.ai/api/v1'
    )
  end
end
