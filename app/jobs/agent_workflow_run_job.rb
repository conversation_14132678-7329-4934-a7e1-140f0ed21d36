# frozen_string_literal: true

class AgentWorkflowRunJob < ApplicationJob
  queue_as :default

  def perform(agent_workflow_run_id)
    @workflow_run = AgentWorkflowRun.find(agent_workflow_run_id)
    @user = User.find(@workflow_run.user_id)

    node_runs = AgentWorkflowNodeRun.joins(:agent_workflow_node)
                                    .where(agent_workflow_run_id: @workflow_run.id)
                                    .order('agent_workflow_nodes.order_level ASC')
                                    .select('agent_workflow_node_runs.*, agent_workflow_nodes.model_bank_id, agent_workflow_nodes.workflow_type')

    agent_runs = node_runs.filter { |node_run| node_run.workflow_type == 'agent' }
    merger_run = node_runs.find { |node_run| node_run.workflow_type == 'merger' }
    @model_banks = ModelBank.where(id: node_runs.map(&:model_bank_id))

    @workflow_run.status = 'running'
    @workflow_run.save!

    @runner_status = 'running'
    agent_runs.each do |agent_run|
      break if @runner_status == 'failed'

      agent_runner(agent_run)
    end

    return if @runner_status == 'failed'

    join_content = agent_runs.map do |agent_run|
      "#{agent_run.reload.data['name']}: #{agent_run.reload.data['response']}"
    end.join("\n")

    merger_run.data['content'] = join_content
    merger_run.save!

    agent_runner(merger_run)

    @workflow_run.status = 'completed'
    @workflow_run.save!
  rescue StandardError => e
    handle_error_workflow(e)
  end

  private

  def agent_runner(agent_run)
    agent_run.status = 'running'
    agent_run.save!

    system_prompt = agent_run.data['system_prompt']
    current_model_bank = @model_banks.find { |model_bank| model_bank.id == agent_run.model_bank_id }

    responses = ''
    raw_responses = []
    usage_metadata = nil

    handle_chunk = proc do |chunk, _bytesize|
      raw_responses << chunk

      content = chunk&.dig('choices', 0, 'delta', 'content')
      responses += content if content.present?

      usage_metadata = chunk&.dig('usage')
    end

    parameters = {
      stream_options: { include_usage: true },
      model: current_model_bank.code,
      messages: [
        { role: 'system', content: system_prompt },
        { role: 'user', content: agent_run.data['content'] }
      ],
      stream: handle_chunk,
      temperature: agent_run.data['temperature']
    }

    client.chat(parameters: parameters)

    agent_run.data['response'] = responses
    agent_run.data['credit_usage'] = credit_usage(usage_metadata, current_model_bank)
    agent_run.response_raw = raw_responses
    agent_run.request_raw = parameters.except(:stream)
    agent_run.usage_metadata = usage_metadata
    agent_run.status = 'completed'
    agent_run.save!
  rescue StandardError => e
    agent_run.status = 'failed'
    agent_run.error_message = e.message
    agent_run.save!

    @runner_status = 'failed'

    handle_error_workflow(e)
  end

  def handle_error_workflow(e)
    raise e if @workflow_run.blank?

    @workflow_run.status = 'failed'
    @workflow_run.error_message = e.message
    @workflow_run.save!
  end

  def credit_usage(usage_metadata, model)
    result = {
      input: 0,
      output: 0
    }

    if usage_metadata.present?
      result[:input] = (usage_metadata['prompt_tokens'] * model.input_rate).ceil(9)
      result[:output] = (usage_metadata['completion_tokens'] * model.output_rate).ceil(9)
    end

    result
  end

  def client
    @client ||= OpenAI::Client.new(
      access_token: Rails.application.credentials.openrouter_key,
      uri_base: 'https://openrouter.ai/api/v1'
    )
  end
end
