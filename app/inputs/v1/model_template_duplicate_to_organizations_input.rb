# frozen_string_literal: true

module V1
  class ModelTemplateDuplicateToOrganizationsInput < ApplicationInput
    required(:organization_ids)

    validate :organization_ids_must_be_array_of_integers

    private

    def organization_ids_must_be_array_of_integers
      return if organization_ids.blank?

      return if organization_ids.all? { |id| id.is_a?(Integer) || id.to_s.match?(/^\d+$/) }

      errors.add(:organization_ids, 'must be an array of valid organization IDs')
    end
  end
end
