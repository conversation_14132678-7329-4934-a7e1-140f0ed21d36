# frozen_string_literal: true

module V1
  class AgentWorkflowOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description
      }
    end

    def detail_format
      format.merge(
        nodes: node_output
      )
    end

    private

    def node_output
      nodes.map do |node|
        V1::AgentWorkflowNodeOutput.new(
          node,
          model_templates: model_templates,
          model_banks: model_banks,
          knowledge_base_files: knowledge_base_files,
          model_template_inputs: model_template_inputs,
          current_user: current_user
        ).format
      end
    end

    def nodes
      @options[:nodes] || []
    end

    def model_templates
      @options[:model_templates] || []
    end

    def model_banks
      @options[:model_banks] || []
    end

    def knowledge_base_files
      @options[:knowledge_base_files] || []
    end

    def model_template_inputs
      @options[:model_template_inputs] || []
    end

    def current_user
      @options[:current_user]
    end
  end
end
