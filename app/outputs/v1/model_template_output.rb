# frozen_string_literal: true

module V1
  class ModelTemplateOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        prompt: @object.prompt,
        model: @object.model,
        template_type: @object.template_type,
        max_tokens: @object.max_tokens,
        credits: @object.max_tokens,
        temperature: @object.temperature,
        instruction: @object.instruction,
        placeholder: @object.placeholder,
        category: @object.category,
        verified: @object.verified,
        owned: owned,
        test_prompt_chat_id: @object.test_prompt_chat_id,
        rating: rating_output,
        organization_prompt: @object.organization_prompt,
        draft: @object.draft,
        reference_output_file: url_output(@object.reference_output_url),
        variables: variables_output,
        instruction_inputs: instruction_inputs_output,
        template_category: template_category_output,
        number_of_used_times: number_of_used_times_output,
        number_of_comments: number_of_comments_output,
        organization_team: organization_team_output,
        organization_id: @object.organization_id,
        organization_name: organization_name_output,
        created_by: user_output,
        parent_id: @object.parent_id,
        parent_name: parent_name_output,
        child_count: @object.child_count,
        is_parent: @object.is_parent?,
        is_child: @object.is_child?,
        children: children_output,
        in_bank: @object.in_bank,
        bank_added_by: bank_added_by_output,
        bank_added_at: @object.bank_added_at,
        bank_notes: @object.bank_notes,
        assigned_organizations: assigned_organizations_output,
        **maybe(:editable, &:editable_output)
      }
    end

    def system_prompt_format
      {
        system_prompt: @object
      }
    end

    private

    def show_editable?
      @options[:show_editable] || false
    end

    def assigned_organizations_output
      return [] if partner_assigned_organizations.blank?

      current_organization_ids = partner_assigned_organizations.filter do |o|
        o.model_template_id == @object.id
      end.map(&:organization_id)

      assigned_organizations.map do |organization|
        next unless current_organization_ids.include? organization.id

        {
          id: organization.id,
          name: organization.name
        }
      end
    end

    def variables_output
      current_variables = variables&.select { |v| v.model_template_id == @object.id }

      current_variables&.map { |v| ModelTemplateVariableOutput.new(v) }
    end

    def instruction_inputs_output
      current_instruction_inputs = instruction_inputs if @object.template_type == 'workflow'
      current_instruction_inputs ||= instruction_inputs&.select { |input| input.model_template_id == @object.id }

      current_instruction_inputs&.map { |input| ModelTemplateInOutput.new(input) }
    end

    def template_category_output
      return if @object.template_category_id.nil? || template_categories.blank?

      current_category = template_categories.find do |tc|
        tc.id == @object.template_category_id
      end

      return if current_category.nil?

      ::V1::TemplateCategoryOutput.new(current_category).format
    end

    def url_output(url)
      return unless url

      uri = URI.parse(url)
      filename = File.basename(uri.path)
      {
        filename: filename,
        url: url
      }
    end

    def number_of_used_times_output
      return 0 unless number_of_used_times

      current_template_used_times = number_of_used_times[@object.id]

      return 0 if current_template_used_times.nil?

      current_template_used_times
    end

    def number_of_comments_output
      return 0 if ratings.blank?

      curr_rating = ratings.find { |r| r[:model_template_id] == @object.id }

      return 0 if curr_rating.blank?

      curr_rating.c.to_i
    end

    def organization_team_output
      return if @object.organization_team_id.nil? || organization_teams.blank?

      curr_org_team = organization_teams.find { |ot| ot.id == @object.organization_team_id }

      return if curr_org_team.nil?

      {
        id: @object.organization_team_id,
        name: curr_org_team.name
      }
    end

    def rating_output
      return 0 if ratings.blank?

      curr_rating = ratings.find { |r| r[:model_template_id] == @object.id }

      return 0 if curr_rating.blank?

      curr_rating.avg.to_f
    end

    def owned
      current_user.id.to_s == @object.user_id
    end

    def user_output
      return if users_with_assigned_team.blank?

      template_user = users_with_assigned_team.find { |u| u.id.to_s == @object.user_id }

      return if template_user.blank?

      ::V1::UserOutput.new(template_user).nano_format
    end

    def children_output
      return [] unless children

      children.select { |child| child.parent_id == @object.id }.map do |child|
        {
          id: child.id,
          name: child.name,
          organization_id: child.organization_id,
          organization_name: child.organization&.name,
          created_at: child.created_at
        }
      end
    end

    # from service.template_user_authority
    def editable_output
      membership = current_user.membership
      template_user = users_with_assigned_team.find { |u| u.id.to_s == @object.user_id }

      template_user_org_team_ids = if template_user.blank?
                                     []
                                   else
                                     template_user.organization_team_ids_agg.flatten.compact
                                   end

      if membership.membership_role == 'team_admin'
        return  (@object.organization_prompt || @object.organization_team_id.present? || @object.user_id == current_user.id.to_s) &&
                (
                  (@object.organization_team_id.present? && membership.organization_team_ids.include?(@object.organization_team_id)) ||
                  (template_user_org_team_ids & membership.organization_team_ids).any? ||
                  @object.user_id == current_user.id.to_s
                )
      elsif membership.membership_role == 'member'
        return  @object.user_id == current_user.id.to_s
      end

      true
    end

    def current_user
      @options[:current_user]
    end

    def variables
      @options[:variables]
    end

    def instruction_inputs
      @options[:instruction_inputs]
    end

    def template_categories
      @options[:template_categories]
    end

    def number_of_used_times
      @options[:number_of_used_times]
    end

    def number_of_comments
      @options[:number_of_comments]
    end

    def organization_teams
      @options[:organization_teams]
    end

    def ratings
      @options[:ratings]
    end

    def users_with_assigned_team
      @options[:users_with_assigned_team]
    end

    def children
      @options[:children]
    end

    def organizations
      @options[:organizations]
    end

    def parent_templates
      @options[:parent_templates]
    end

    def assigned_organizations
      @options[:assigned_organizations] || []
    end

    def partner_assigned_organizations
      @options[:partner_assigned_organizations] || []
    end

    def organization_name_output
      return if @object.organization_id.nil? || organizations.blank?

      curr_org = organizations.find { |o| o.id == @object.organization_id }

      return if curr_org.nil?

      curr_org.name
    end

    def parent_name_output
      return if @object.parent_id.nil? || parent_templates.blank?

      curr_parent = parent_templates.find { |pt| pt.id == @object.parent_id }

      return if curr_parent.nil?

      curr_parent.name
    end

    def bank_added_by_output
      return unless @object.bank_added_by.present?

      bank_user = bank_added_by_users&.find { |u| u.id.to_s == @object.bank_added_by }
      return unless bank_user

      ::V1::UserOutput.new(bank_user).nano_format
    end

    def bank_added_by_users
      @options[:bank_added_by_users]
    end
  end
end
