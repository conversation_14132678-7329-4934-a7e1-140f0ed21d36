# frozen_string_literal: true

module V1
  class AgentWorkflowNodeOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        workflow_type: @object.workflow_type,
        reference_output: @object.reference_output,
        rules: @object.rules,
        order_level: @object.order_level,
        model_template: model_template_output,
        model: model_bank_output,
        knowledge_base_file: knowledge_base_file_output
      }
    end

    private

    def model_template_output
      current_template = model_templates.find { |template| template.id == @object.model_template_id }

      return {} unless current_template

      V1::ModelTemplateOutput.new(
        current_template,
        instruction_inputs: model_template_inputs,
        current_user: current_user
      ).format
    end

    def model_bank_output
      current_model_bank = model_banks.find { |model_bank| model_bank.id == @object.model_bank_id }

      return {} unless current_model_bank

      {
        id: current_model_bank.id,
        model: current_model_bank.name,
        code: current_model_bank.code
      }
    end

    def knowledge_base_file_output
      current_file = knowledge_base_files.find { |file| file.id == @object.knowledge_base_file_id }

      return {} unless current_file

      V1::KnowledgeBaseOutput.new(current_file).format
    end

    def knowledge_base_files
      @options[:knowledge_base_files] || []
    end

    def model_banks
      @options[:model_banks] || []
    end

    def model_templates
      @options[:model_templates] || []
    end

    def model_template_inputs
      @options[:model_template_inputs] || []
    end

    def current_user
      @options[:current_user]
    end
  end
end
