# frozen_string_literal: true

module V1
  class ModelBankOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        code: @object.code,
        input_rate: @object.input_rate,
        output_rate: @object.output_rate,
        web_search_rate: @object.web_search_rate,
        image_rate: @object.image_rate,
        file_rate: @object.file_rate,
        status: @object.status
      }
    end
  end
end
