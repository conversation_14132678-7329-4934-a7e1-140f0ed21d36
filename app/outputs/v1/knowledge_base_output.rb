# frozen_string_literal: true

module V1
  class KnowledgeBaseOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        file_url: @object.file_url,
        filename: @object.filename,
        content_type: @object.content_type,
        file_size: @object.file_size,
        is_active: @object.is_active,
        file_type: @object.file_type,
        download_url: @object.download_url,
        organization_id: @object.organization_id,
        # Ragie.ai fields
        ragie_document_id: @object.ragie_document_id,
        ragie_status: @object.ragie_status,
        ragie_processed_at: @object.ragie_processed_at,
        ragie_error_message: @object.ragie_error_message,
        created_at: @object.created_at,
        updated_at: @object.updated_at
      }
    end

    def plan_limit_format
      {
        max_files: @object.max_files,
        current_files: @object.current_files,
        remaining_files: @object.remaining_files
      }
    end
  end
end
