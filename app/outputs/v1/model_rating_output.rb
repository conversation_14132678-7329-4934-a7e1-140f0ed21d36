# frozen_string_literal: true

module V1
  class ModelRatingOutput < ApiOutput
    def format
      {
        id: @object.id,
        rating: @object.rating,
        comment: @object.comment,
        feedback: @object.feedback,
        commented_at: @object.created_at,
        model_template: model_template_output,
        organization: organization_output,
        user: user_output,
        number_of_used_times: number_of_used_times_output,
        model: model_used_output
      }
    end

    private

    def organization_output
      model_template = model_templates.find { |mt| mt.id.to_s == @object.model_template_id.to_s }

      org = organizations.find { |mt| mt.id.to_s == model_template&.organization_id.to_s }

      return if org.blank?

      {
        id: org&.id,
        name: org&.name
      }
    end

    def model_template_output
      model_template = model_templates.find { |mt| mt.id.to_s == @object.model_template_id.to_s }

      return if model_template.blank?

      category = template_categories.find { |tc| tc.id.to_s == model_template.template_category_id.to_s }

      {
        id: model_template.id,
        name: model_template.name,
        category: {
          id: category&.id,
          name: category&.name
        }
      }
    end

    def user_output
      current_rating_user = users.find { |u| u.id.to_s == @object.user_id.to_s }

      return if current_rating_user.nil?

      role = memberships.find { |m| m.user_id.to_s == current_rating_user.id.to_s }&.membership_role

      {
        id: current_rating_user.id,
        name: current_rating_user.display_name,
        role: role
      }
    end

    def number_of_used_times_output
      return 0 unless number_of_used_times

      current_template_used_times = number_of_used_times[@object.user_id]

      return 0 if current_template_used_times.nil?

      current_template_used_times
    end

    def model_used_output
      model_used = messages.find { |m| m.id.to_s == @object.message_id.to_s }&.model

      return if model_used.blank?

      model_used
    end

    def number_of_used_times
      @options[:number_of_used_times]
    end

    def users
      @options[:users] || []
    end

    def model_templates
      @options[:model_templates] || []
    end

    def template_categories
      @options[:template_categories] || []
    end

    def memberships
      @options[:memberships] || []
    end

    def organizations
      @options[:organizations] || []
    end

    def messages
      @options[:messages] || []
    end
  end
end
