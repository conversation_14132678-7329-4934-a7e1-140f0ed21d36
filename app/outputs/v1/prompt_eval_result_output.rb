# frozen_string_literal: true

module V1
  class PromptEvalResultOutput < ApiOutput
    def format
      {
        id: @object.id,
        prompt_eval_id: @object.prompt_eval_id,
        model_bank: model_bank_output,
        result_text: @object.result_text,
        status: @object.status,
        error_message: @object.error_message,
        response_raw: @object.response_raw
      }
    end

    def detail_format
      format.merge(
        prompt_eval: prompt_eval_output,
        model_bank: model_bank_output
      )
    end

    private

    def model_bank_output
      ::V1::ModelBankOutput.new(@object.model_bank).format
    end

    def prompt_eval_output
      ::V1::PromptEvalOutput.new(@object.prompt_eval).format
    end
  end
end
