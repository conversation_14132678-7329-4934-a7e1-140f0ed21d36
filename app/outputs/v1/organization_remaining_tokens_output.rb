# frozen_string_literal: true

module V1
  class OrganizationRemainingTokensOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        purchased_credits: @object.purchased_credits.to_f,
        remaining_monthly_credits: @object.remaining_monthly_credits.to_f,
        monthly_credits_refresh: @object.monthly_credits_refresh,
        refresh_date: @object.refresh_date
      }
    end
  end
end
