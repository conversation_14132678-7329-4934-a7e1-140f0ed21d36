# frozen_string_literal: true

module V1
  class UserManagementOutput < ApiOutput
    def create_invite_format
      {
        email: @object.email,
        invitation_status: @object.invitation_status,
        invitation_expiry_date: @object.invitation_expiry_date,
        role: @object.role
      }
    end

    def create_change_password_request_format
      {
        email: @object.email,
        request_status: @object.request_status,
        request_expiry_date: @object.request_expiry_date
      }
    end

    def invitation_format
      {
        email: @object.email,
        invitation_status: @object.invitation_status,
        invitation_expiry_date: @object.invitation_expiry_date,
        invitation_code: @object.invitation_code,
        organization: organization_output,
        invited_by: invited_by_output
      }
    end

    def invited_by_output
      invited_by = @object.invited_by_membership&.user

      return unless invited_by.present?

      {
        id: invited_by.id,
        name: invited_by.display_name
      }
    end

    def organization_output
      return if @object.organization_id.nil?

      {
        id: @object.organization_id,
        name: @object.organization&.name
      }
    end
  end
end
