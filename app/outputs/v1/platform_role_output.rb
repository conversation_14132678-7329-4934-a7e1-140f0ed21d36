# frozen_string_literal: true

module V1
  class PlatformRoleOutput < ApiOutput
    def format
      {
        id: @object.id,
        email: @object.user.email,
        role: @object.membership_role,
        user: user_output,
        organization: organization_output,
        primary_organization: managed_organizations_output.first,
        managed_organizations: managed_organizations_output.drop(1),
        organization_team: @object.organization_team
      }
    end

    private

    def managed_organizations_output
      return [] if @object.managed_organizations.blank?

      organizations = @object.managed_organizations.compact
      organizations.map do |organization|
        {
          id: organization.id,
          name: organization.name,
          code: organization.code

        }
      end
    end

    def user_output
      user = @object.user

      return nil if user.blank?

      {
        id: user.id,
        display_name: user.display_name,
        name: user.display_name,
        photo_url: user.photo_url,
        email: user.email
      }
    end

    def organization_output
      organization = @object.organization

      return nil if organization.blank?

      {
        id: organization.id,
        name: organization.name,
        code: organization.code
      }
    end

    def managed_organizations
      @options[:managed_organizations] || []
    end
  end
end
