# frozen_string_literal: true

module V1
  class PromptEvalOutput < ApiOutput
    def format
      {
        id: @object.id,
        prompt: @object.prompt,
        params: @object.params,
        request_raw: @object.request_raw,
        user_id: @object.user_id,
        model_template_id: @object.model_template_id,
        **maybe(:prompt_eval_results) { prompt_eval_results_output }
      }
    end

    def show_format
      {
        id: @object.id,
        prompt: @object.prompt,
        params: @object.params,
        status: eval_status,
        request_raw: @object.request_raw
      }
    end

    private

    def prompt_eval_results_output
      return [] if prompt_eval_results.blank?

      prompt_eval_results.map do |prompt_eval_result|
        ::V1::PromptEvalResultOutput.new(prompt_eval_result).format
      end
    end

    def show_prompt_eval_results?
      return false if prompt_eval_results.blank?
      return false if @object.model_template_id.present?

      true
    end

    def prompt_eval_results
      @options[:prompt_eval_results]
    end

    def eval_status
      @options[:eval_status]
    end
  end
end
