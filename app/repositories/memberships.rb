# frozen_string_literal: true

class Memberships < ApplicationRepository
  sort_by :id, :desc

  def default_scope
    join_sql = <<~SQL
      LEFT JOIN user_invitations ON user_invitations.email = memberships.invited_email
        AND user_invitations.organization_id = memberships.organization_id
    SQL

    ::Membership.joins(join_sql).select('memberships.*, user_invitations.invitation_status')
  end

  def filter_by_organization_id(organization_id)
    @scope.where('memberships.organization_id = ?', organization_id)
  end

  def filter_by_organization_team_id(org_team_id)
    @scope.where('? = ANY(organization_team_ids)', org_team_id)
  end

  def filter_by_role(role)
    @scope.where(role: role)
  end

  def filter_by_exclude_platform_admins(exclude_platform_admins)
    return @scope unless exclude_platform_admins

    # Exclude platform admins (roles with negative values: owner_platform, super_admin_platform, admin)
    @scope.where('role >= 0')
  end
end
