# frozen_string_literal: true

class KnowledgeBaseFiles < ApplicationRepository
  # Default sorting by created_at desc
  sort_by :created_at, :desc

  def default_scope
    KnowledgeBaseFile.kept
  end

  def filter_by_organization_id(organization_id)
    @scope.by_organization(organization_id)
  end

  def filter_by_active_only(active_only)
    case active_only
    when true, 'true'
      @scope.active
    when false, 'false'
      @scope.where(is_active: false)
    else
      @scope
    end
  end

  def filter_by_search(search)
    @scope.where('name ILIKE ?', "%#{search}%")
  end

  def filter_by_file_type(file_type)
    @scope.where(file_type: file_type)
  end

  def filter_by_ragie_status(ragie_status)
    @scope.where(ragie_status: ragie_status)
  end

  # Default sorting by created_at desc
  def filter_by_sort_column(sort_column)
    case sort_direction
    when 'asc' then @scope.reorder(order_asc(sort_column))
    when 'desc' then @scope.reorder(order_desc(sort_column))
    end
  end

  def filter_by_sort_direction(direction)
    case direction
    when 'asc' then @scope.reorder(order_asc(sort_column))
    when 'desc' then @scope.reorder(order_desc(sort_column))
    end
  end
end
