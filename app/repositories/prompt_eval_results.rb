# frozen_string_literal: true

class PromptEvalResults < ApplicationRepository
  sort_by :created_at, :desc

  def default_scope
    ::PromptEvalResult.all
  end

  def filter_by_status(status)
    @scope.where(status: status)
  end

  def filter_by_prompt_eval_id(prompt_eval_id)
    @scope.where(prompt_eval_id: prompt_eval_id)
  end

  def filter_by_model_bank_id(model_bank_id)
    @scope.where(model_bank_id: model_bank_id)
  end

  def filter_by_model_template_id(model_template_id)
    @scope.joins(:prompt_eval).where(prompt_evals: { model_template_id: model_template_id })
  end
end
