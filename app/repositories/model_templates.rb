# frozen_string_literal: true

class ModelTemplates < ApplicationRepository
  # TODO: add pagination
  sort_by :verified, :desc

  def default_scope
    ::ModelTemplate.all
  end

  def filter_by_template_type(type)
    @scope.where(template_type: type)
  end

  def filter_by_draft(draft)
    @scope.where(draft: draft)
  end

  def filter_by_verified(verified)
    @scope.where(verified: verified)
  end

  def filter_by_organization_prompt(organization_prompt)
    @scope.where(organization_prompt: organization_prompt)
  end

  def filter_by_category(category)
    @scope.where('? = ANY(model_templates.category)', category)
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_workspace_id(workspace_id)
    @scope.where(workspace_id: workspace_id)
  end

  def filter_by_template_category_id(template_category_id)
    @scope.where(template_category_id: template_category_id)
  end

  def filter_by_organization_team_id(organization_team_id)
    @scope.where(organization_team_id: organization_team_id)
  end

  def filter_by_search(search)
    @scope.where('name ilike ?', "%#{search}%")
  end

  def filter_by_parent_id(parent_id)
    @scope.where(parent_id: parent_id)
  end

  def filter_by_parent_id_not(parent_id_not)
    @scope.where.not(parent_id: parent_id_not)
  end

  def filter_by_id(id)
    @scope.where(id: id)
  end

  def filter_by_in_bank(in_bank)
    @scope.where(in_bank: in_bank)
  end

  def filter_by_bank_added_by(bank_added_by)
    @scope.where(bank_added_by: bank_added_by)
  end
end
