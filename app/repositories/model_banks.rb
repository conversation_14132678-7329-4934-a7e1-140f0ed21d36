# frozen_string_literal: true

class ModelBanks < ApplicationRepository
  sort_by :name, :asc

  def default_scope
    ::ModelBank.all
  end

  def filter_by_status(status)
    @scope.where(status: status)
  end

  def filter_by_search(search)
    @scope.where('name ILIKE ? OR code ILIKE ?', "%#{search}%", "%#{search}%")
  end

  def filter_by_rate_range(min_rate, max_rate)
    @scope.where('input_rate + output_rate + web_search_rate + image_rate + file_rate BETWEEN ? AND ?', min_rate,
                 max_rate)
  end
end
