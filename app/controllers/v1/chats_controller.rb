# frozen_string_literal: true

module V1
  class ChatsController < ApiController
    authorize_auth_token! :all

    def create
      request_body[:workspace_id] ||= params[:workspace_id]
      input = ::V1::ChatCreationInput.new(request_body)
      validate! input, capture_failure: true

      chat = service.create(input.output)

      render_json chat, ::V1::ChatOutput, use: :full_format, status: :created
    end

    def list
      query_params[:workspace_id] = params[:workspace_id]
      result = service.list(query_params)
      metadata = { workspace_id: result[:workspace_id] }

      render_json_array result[:chats], ::V1::ChatOutput, use: :format, metadata: metadata
    end

    def update
      input = ::V1::ChatUpdateInput.new(request_body)
      validate! input, capture_failure: true

      chat = service.update(params[:id], input.output)

      render_json chat, ::V1::ChatOutput, use: :full_format
    end

    def show
      chat = ::Chat.find_by!(id: params[:chat_id], workspace_id: params[:workspace_id])

      render_json chat, ::V1::ChatOutput, use: :full_format
    end

    def destroy
      chat = ::Chat.find_by!(id: params[:id])

      chat.discard!

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::ChatOutput
    end

    def service
      @service ||= ::ChatService.new(current_user)
    end
  end
end
