# frozen_string_literal: true

module V1
  class PromptEvalsController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.prompt_evals
    end

    def show
      result = service.show(params[:id])

      render_json result.prompt_eval, eval_status: result.status, use: :show_format
    end

    def evaluate
      input = ::V1::PromptEvalEvaluateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.evaluate(input.output)

      render_json result, status: :created
    end

    private

    def default_output
      ::V1::PromptEvalOutput
    end

    def service
      @service ||= ::PromptEvalService.new(current_user)
    end
  end
end
