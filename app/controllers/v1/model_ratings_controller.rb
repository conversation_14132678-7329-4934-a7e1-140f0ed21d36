# frozen_string_literal: true

module V1
  class ModelRatingsController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::ModelRatingCreationInput.new(request_body)
      validate! input, capture_failure: true

      chat = service.create(input.output)

      render_json chat, status: :created
    end

    def update
      input = ::V1::ModelRatingUpdateInput.new(request_body)
      validate! input, capture_failure: true

      chat = service.update(params[:id], input.output)

      render_json chat
    end

    def index
      result = service.index(query_params)

      options = {
        users: result.users,
        model_templates: result.model_templates,
        template_categories: result.template_categories,
        memberships: result.memberships,
        messages: result.messages,
        organizations: result.organizations
      }

      render_json_array result.model_ratings, **options
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({})
    end

    def model_rating_results
      result = service.model_rating_results(query_params)

      timestamp = Time.now.strftime('%Y%m%d%H%M%S')
      respond_to do |format|
        format.csv { send_data result, filename: "agent_analytics_results_#{timestamp}.csv" }
      end
    end

    private

    def service
      @service ||= ::ModelRatingService.new(current_user)
    end

    def default_output
      ::V1::ModelRatingOutput
    end
  end
end
