# frozen_string_literal: true

module V1
  class PlatformRolesController < ApiController
    authorize_auth_token! :all

    def list_admin_platform_roles
      result = service.list_admin_platform_roles
      render_json_array result.memberships, use: :format
    end

    private

    def default_output
      V1::PlatformRoleOutput
    end

    def service
      @service ||= PlatformRoleService.new(current_user, current_org_id, selected_org_id)
    end
  end
end
