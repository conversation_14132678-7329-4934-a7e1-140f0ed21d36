# frozen_string_literal: true

module V1
  class PromptEvalResultsController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.prompt_eval_results, use: :detail_format
    end

    def show
      result = service.show(params[:id])
      render_json result
    end

    private

    def default_output
      ::V1::PromptEvalResultOutput
    end

    def service
      @service ||= ::PromptEvalResultService.new(current_user)
    end
  end
end
