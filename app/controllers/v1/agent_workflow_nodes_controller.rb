# frozen_string_literal: true

module V1
  class AgentWorkflowNodesController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::AgentWorkflowNode::CreationInput.new(request_body)
      validate! input, capture_failure: true

      agent_workflow_node = service.create(input.output)
      render_json agent_workflow_node, status: :created
    end

    def update
      input = ::V1::AgentWorkflowNode::UpdateInput.new(request_body)
      validate! input, capture_failure: true

      agent_workflow_node = service.update(path_params[:id], input.output)
      render_json agent_workflow_node
    end

    private

    def service
      @service ||= AgentWorkflowNodeService.new(current_user)
    end

    def default_output
      ::V1::AgentWorkflowNodeOutput
    end
  end
end
