# frozen_string_literal: true

module V1
  class MembershipsController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.memberships,
                        users: result.users,
                        organization_teams: result.organization_teams
    end

    def update
      input = ::V1::MembershipUpdateInput.new(request_body)
      validate! input, capture_failure: true

      membership = service.update(params[:id], input.output)

      render_json membership, use: :format, status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::MembershipOutput
    end

    def service
      @service ||= ::MembershipService.new(current_user,current_org_id,selected_org_id)
    end
  end
end
