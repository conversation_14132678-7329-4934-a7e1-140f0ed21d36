# frozen_string_literal: true

module V1
  class ModelTemplateBanksController < ApiController
    authorize_auth_token! :all

    def add_to_bank
      input = ::V1::AddToBankInput.new(request_body)
      validate! input, capture_failure: true

      result = service.add_to_bank(params[:model_template_id], input.output[:notes])

      render_json result.template, current_user:, message: result.message
    end

    def remove_from_bank
      result = service.remove_from_bank(params[:model_template_id])

      render_json result.template, current_user:, message: result.message
    end

    def duplicate_to_organizations
      input = ::V1::ModelTemplateDuplicateToOrganizationsInput.new(request_body)
      validate! input, capture_failure: true

      result = service.duplicate_to_organizations(params[:parent_id], input.output[:organization_ids])

      options = {
        current_user: current_user,
        variables: result.parent_template.model_template_variables,
        instruction_inputs: result.parent_template.model_template_ins,
        template_categories: [result.parent_template.template_category].compact,
        organization_teams: [result.parent_template.organization_team].compact,
        children: result.duplicated_templates
      }

      render_json result.parent_template, **options
    end

    def update_parent_template
      input = ::V1::ModelTemplateUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update_parent_template(params[:id], input.output)

      options = {
        current_user: current_user,
        variables: result.variables,
        template_categories: result.template_categories
      }

      render_json result.template, **options
    end

    def destroy_parent_template
      service.destroy_parent_template(params[:id])

      render_empty_json({}, status: :ok)
    end

    def duplicate_parent_template
      result = service.duplicate_parent_template(params[:id])

      options = {
        current_user: current_user,
        variables: result.variables,
        template_categories: result.template_categories
      }

      render_json result.duplicated_template, **options
    end

    def assign_parent_template
      input = ::V1::ModelTemplateAssignParentInput.new(request_body)
      validate! input, capture_failure: true

      result = service.assign_parent_template(params[:id], input.output)

      options = {
        current_user: current_user,
        variables: result.variables,
        template_categories: result.template_categories
      }

      render_json result.template, **options
    end

    private

    def service
      @service ||= ModelTemplateService.new(current_user)
    end

    def default_output
      V1::ModelTemplateOutput
    end
  end
end
