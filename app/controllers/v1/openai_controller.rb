# frozen_string_literal: true

module V1
  class OpenaiController < ApiController
    include ActionController::Live

    authorize_auth_token! :all

    def stream_response_v2
      input = ::V2::ChatCompletionInput.new(request_body)
      validate! input, capture_failure: true

      response.headers['Content-Type'] = 'text/event-stream'
      response.headers['Last-Modified'] = Time.now.httpdate

      sse = SSE.new(response.stream)

      chat_params = {}
      input_params = input.output

      chat_params = service.initialize_chat_v2(input_params)

      if chat_params[:workflow_id].present? && input_params[:query_list].present?
        chat_params[:query_list] = input_params[:query_list]
        agent_service.stream(sse, chat_params)
      else
        service.stream_response_v2(sse, chat_params)
      end
    rescue StandardError => e
      sse&.close
      raise e
    ensure
      sse&.close
      service.update_remaining_tokens_v2(chat_params)
    end

    def chat_stream_v2
      input = ::V2::ChatStreamInput.new(request_body)
      validate! input, capture_failure: true

      output = input.output
      chat_params = service.initialize_chat_v2(output)

      response.headers['Content-Type'] = 'text/event-stream'
      response.headers['Last-Modified'] = Time.now.httpdate

      sse = SSE.new(response.stream)

      service.stream_response_v2(sse, chat_params)
    ensure
      sse&.close
      service.update_remaining_tokens_v2(chat_params)
    end

    def show_workspace
      workspace = service.show_chat(params[:workspace_id])

      render_json workspace, ::V1::WorkspaceOutput, use: :format
    end

    private

    def default_output
      ::V1::MessageOutput
    end

    def service
      @service ||= ::OpenrouterService.new(current_user)
    end

    def agent_service
      @agent_service ||= ::AgentWorkflowRunService.new(current_user)
    end
  end
end
