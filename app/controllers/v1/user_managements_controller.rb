# frozen_string_literal: true

module V1
  class UserManagementsController < ApiController
    authorize_auth_token! :all, only: %i[create_invitation list_invited_users]

    def create_invitation
      input = ::V1::UserManagementInvitationInput.new(request_body)
      validate! input, capture_failure: true

      invitation = service.create_invitation(input.output)

      render_json invitation, use: :create_invite_format, status: :created
    end

    def accept_invitation
      input = ::V1::UserManagementAcceptInviteInput.new(request_body)
      validate! input, capture_failure: true

      user = service.accept_invitation(input.output)

      render_json user, ::V1::UserOutput, status: :created
    end

    def detail_invitation
      invitation = service.detail_invitation(query_params)

      render_json_array invitation,
                        use: :invitation_format
    end

    def change_password_request
      input = ::V1::UserManagementChangePasswordRequestInput.new(request_body)
      validate! input, capture_failure: true

      request = service.change_password_request(input.output)

      render_json request, use: :create_change_password_request_format, status: :created
    end

    def change_password
      input = ::V1::UserManagementChangePasswordInput.new(request_body)
      validate! input, capture_failure: true

      service.change_password(input.output)

      render_empty_json({}, status: :ok)
    end

    def list_invited_users
      result = service.list_invited_users(query_params)

      render_json_array result, use: :invitation_format
    end

    private

    def default_output
      ::V1::UserManagementOutput
    end

    def service
      @service ||= ::UserManagementService.new(current_user)
    end
  end
end
