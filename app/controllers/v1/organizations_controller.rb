# frozen_string_literal: true

module V1
  class OrganizationsController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::OrganizationCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(input.output)

      render_json result.organization,
                  use: :format, status: :created,
                  organizations_plans_thresholds: result.organizations_plans_thresholds
    end

    def update
      input = ::V1::OrganizationUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.organization,
                  use: :format, status: :ok,
                  organizations_plans_thresholds: result.organizations_plans_thresholds
    end

    def show
      result = service.show(params[:id])

      render_json result.organization,
                  use: :format,
                  organizations_plans_thresholds: result.organizations_plans_thresholds
    end

    def index
      result = service.index(query_params)

      render_json_array result.organizations,
                        use: :format, status: :ok,
                        organizations_plans_thresholds: result.organizations_plans_thresholds,
                        owner_memberships: result.owner_memberships
    end

    def transfer_ownership
      input = ::V1::OrganizationTransferOwnershipInput.new(request_body)
      validate! input, capture_failure: true

      service.transfer_ownership(params[:organization_id], input.output)

      render_empty_json({}, status: :ok)
    end

    def remaining_tokens
      result = service.get_remaining_tokens(params[:organization_id])
      render_json result,
                  ::V1::OrganizationRemainingTokensOutput,
                  use: :format,
                  status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::OrganizationOutput
    end

    def service
      @service ||= ::OrganizationService.new(current_user, current_org_id, selected_org_id)
    end
  end
end
