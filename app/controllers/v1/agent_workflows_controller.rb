# frozen_string_literal: true

module V1
  class AgentWorkflowsController < ApiController
    authorize_auth_token! :all

    def index
      agent_workflows = service.index(query_params)
      render_json_array agent_workflows, use: :format
    end

    def show
      result = service.show(params[:id])

      options = {
        use: :detail_format,
        nodes: result.nodes,
        model_templates: result.model_templates,
        model_banks: result.model_banks,
        knowledge_base_files: result.knowledge_base_files,
        model_template_inputs: result.model_template_inputs,
        current_user: current_user
      }

      render_json result.agent_workflow, **options
    end

    def create
      input = ::V1::AgentWorkflow::CreationInput.new(request_body)
      validate! input, capture_failure: true

      agent_workflow = service.create(input.output)

      render_json agent_workflow, use: :format, status: :created
    end

    def update
      input = ::V1::AgentWorkflow::UpdateInput.new(request_body)
      validate! input, capture_failure: true

      agent_workflow = service.update(params[:id], input.output)

      render_json agent_workflow, use: :format, status: :ok
    end

    def destroy
      result = service.destroy(params[:id])
      render_json result
    end

    private

    def service
      @service ||= AgentWorkflowService.new(current_user)
    end

    def default_output
      ::V1::AgentWorkflowOutput
    end
  end
end
