# frozen_string_literal: true

module V1
  class KnowledgeBaseFilesController < <PERSON>pi<PERSON>ontroller
    authorize_auth_token! :all

    before_action :authorize_target_organization!, only: %i[create update show index destroy plan_limits]

    def create
      input = V1::KnowledgeBaseFileCreationInput.new(request_body)
      validate! input, capture_failure: true

      knowledge_base = target_service.create(input.output)

      render_json knowledge_base, status: :created
    end

    def update
      input = V1::KnowledgeBaseFileUpdateInput.new(request_body)
      validate! input, capture_failure: true

      knowledge_base = target_service.update(params[:id], input.output)

      render_json knowledge_base
    end

    def show
      knowledge_base = target_service.show(params[:id])
      render_json knowledge_base
    end

    def index
      result = target_service.list(query_params)
      render_json_array result.knowledge_bases, use: :format
    end

    def destroy
      knowledge_base = target_service.destroy(params[:id])
      render_json knowledge_base
    end

    def plan_limits
      # Determine target organization
      target_organization =
        if query_params[:organization_id].present?
          Organization.find(query_params[:organization_id])
        else
          current_organization
        end

      authorize_organization_access!(target_organization)

      # Create a new service instance with the target organization
      target_service = KnowledgeBaseFilesService.new(target_organization)
      plan_limits = target_service.get_plan_limits

      render_json plan_limits, use: :plan_limit_format
    end

    private

    def default_output
      V1::KnowledgeBaseOutput
    end

    def authorize_target_organization!
      authorize_organization_access!(target_organization)
    end

    def service
      @service ||= ::KnowledgeBaseFilesService.new(current_organization)
    end

    def target_service
      @target_service ||= ::KnowledgeBaseFilesService.new(target_organization)
    end

    def target_organization
      @target_organization ||= if organization_id_param.present?
                                 Organization.find(organization_id_param)
                               else
                                 current_organization
                               end
    end

    def organization_id_param
      # Check query params first (for GET requests), then request body (for POST/PUT requests)
      query_params[:organization_id] || params[:organization_id]
    end

    def current_organization
      current_user.membership.organization
    end

    def platform_admin?
      return false unless current_user&.membership

      %w[admin super_admin_platform owner_platform].include?(current_user.membership.membership_role)
    end

    def authorize_organization_access!(organization)
      # Allow platform admins to access any organization
      return if platform_admin?

      # Allow partner admins to access organizations they created
      if current_user.role == 'partner_admin'
        user_id = current_user.id.is_a?(Integer) ? current_user.id.to_s : current_user.id
        return if organization.created_by_id == user_id
      end

      # Allow users to access their own organization
      return if organization.id == current_user.membership.organization_id

      raise ExceptionHandler::Unauthorized, 'Not authorized to access this organization'
    end
  end
end
