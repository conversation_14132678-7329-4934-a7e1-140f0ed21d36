# frozen_string_literal: true

class ApplicationMailer < ActionMailer::Base
  default from: ENV.fetch('MAILER_EMAIL_ADDRESS', '<EMAIL>')
  layout 'mailer'

  def vocal_first?(text)
    return false unless text

    return true if text.is_a?(String) && text.match?(/^[aiueo]/i)

    false
  end

  def map_humanize_role(role)
    role_mappings = {
      owner_platform: 'Owner Platform',
      super_admin_platform: 'Super Admin',
      admin: 'Admin',
      super_user: 'Super User',
      partner_admin: 'Partner Channel',
      owner: 'Workspace Owner',
      super_admin: 'Admin',
      team_admin: 'Admin',
      member: 'Member'
    }

    role_mappings[role.to_sym] || role.to_s.humanize
  end
end
