# frozen_string_literal: true

class OnboardOrganizationMailer < ApplicationMailer
  def send_email
    @membership = params[:membership]
    @user = @membership.user
    @user_name = @user.display_name.split(' ').first
    @organization = @membership.organization
    @base_app = ENV.fetch('WEBAPP', nil)
    @cs_email = ENV.fetch('CUSTOMER_SERVICE_EMAIL_ADDRESS', nil)

    subject = 'Welcome to TuneAI! Your Account Is Ready'

    mail(to: @user.email, subject: subject)
  end
end
