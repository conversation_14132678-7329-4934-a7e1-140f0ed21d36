# frozen_string_literal: true

class InviteMemberNotificationMailer < ApplicationMailer
  def send_email
    user_invitation = params[:user_invitation]
    invited_by_user = user_invitation.invited_by_membership.user
    organization = user_invitation.organization
    send_to_email = invited_by_user.email
    invited_role = Membership.role_mappings.key(user_invitation.role).titleize

    @invitee_name = invited_by_user.display_name.split.first
    @code = organization.code
    @user_email = user_invitation.email
    @user_role = vocal_first?(invited_role) ? "an #{invited_role}" : "a #{invited_role}"

    @base_app = ENV.fetch('ADMIN_APP', nil)
    @member_setting_app_path = "#{@base_app}/settings/organization/members"

    mail(to: send_to_email, subject: "You've Invited a New Team Member to BrandRev.ai")
  end
end
