# frozen_string_literal: true

class UserInvitationMailer < ApplicationMailer
  def send_email
    user_invitation = params[:user_invitation]
    invited_by_user = user_invitation.invited_by_membership.user
    organization = user_invitation.organization
    assigned_role = Membership.role_mappings.key(user_invitation.role)
    base_app = platform_admin?(assigned_role) ? admin_app : web_app
    humanized_role = map_humanize_role(assigned_role)

    @organization_name = organization.name
    @organization_code = organization.code
    @user_name = user_invitation.email.split('@').first
    @invitee_email = invited_by_user.email
    @accept_invite_link = "#{base_app}/accept-invitation?invite_code=#{user_invitation.invitation_code}&code=#{@organization_code}&email=#{user_invitation.email}&name=#{@user_name}"
    @brandrev_link = base_app
    @brandrev_website = ENV['BRANDREV_WEBSITE'] || 'https://brandrev.ai'
    @user_role = vocal_first?(humanized_role) ? "an #{humanized_role}" : "a #{humanized_role}"

    if assigned_role == 'partner_admin'
      template_mailer = 'partner_admin'
    elsif platform_admin?(assigned_role)
      template_mailer = 'admin_platform'
    end

    options = {
      to: user_invitation.email,
      subject: "You've Been Invited to Join #{subject_name}"
    }

    options[:template_path] = "user_invitation_mailer/#{template_mailer}" if template_mailer.present?

    mail(**options)
  end

  private

  def platform_admin?(role)
    %w[admin super_admin_platform owner_platform partner_admin].include?(role)
  end

  def web_app
    ENV['WEBAPP'] || 'https://app.tuneai.com'
  end

  def admin_app
    ENV['ADMIN_APP'] || 'https://admin.tuneai.com'
  end

  def subject_name
    ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'
  end
end
