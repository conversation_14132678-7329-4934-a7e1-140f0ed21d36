# frozen_string_literal: true

class PasswordChangeRequestMailer < ApplicationMailer
  def send_email
    token_request = params[:token_request]
    user_role = token_request.user.role
    organization = token_request.user.memberships.first.organization

    @base_app = admin_platform(user_role) ? admin_app : web_app
    @user_name = token_request.user.display_name.split.first
    @specific_platform = admin_platform(user_role) ? 'Admin Platform' : "#{organization.code} Workspace"
    @brandrev_website = ENV['BRANDREV_WEBSITE'] || 'https://brandrev.ai'

    @request_link = "#{@base_app}/reset-password?request_code=#{token_request.request_code}&code=#{organization.code}"

    mail(to: token_request.email, subject: "You Have Requested to Change Your #{subject_name} Account's Password")
  end

  private

  def subject_name
    ENV['SUBJECT_BRAND_NAME'] || 'BrandRev.ai'
  end

  def admin_platform(role)
    %w[admin super_admin_platform owner_platform partner_admin].include?(role)
  end

  def admin_app
    ENV['ADMIN_APP'] || 'https://admin.brandrev.ai'
  end

  def web_app
    ENV['WEBAPP'] || 'https://app.brandrev.ai'
  end
end
