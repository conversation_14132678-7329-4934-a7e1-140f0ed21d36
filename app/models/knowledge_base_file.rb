# frozen_string_literal: true

class KnowledgeBaseFile < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization

  validates :name, presence: true
  validates :filename, presence: true
  validates :organization, presence: true

  # Ragie.ai status constants
  RAGIE_STATUSES = %w[pending processing ready failed].freeze

  scope :active, -> { where(is_active: true) }
  scope :by_organization, ->(organization_id) { where(organization_id: organization_id) }

  # Ragie.ai scopes (for future implementation)
  scope :ragie_pending, -> { where(ragie_status: 'pending') }
  scope :ragie_processing, -> { where(ragie_status: 'processing') }
  scope :ragie_ready, -> { where(ragie_status: 'ready') }
  scope :ragie_failed, -> { where(ragie_status: 'failed') }
  scope :ragie_processed, -> { where(ragie_status: %w[ready failed]) }

  def file_extension
    File.extname(filename).downcase if filename.present?
  end

  def is_pdf?
    file_extension == '.pdf'
  end

  def is_text?
    %w[.txt .md .doc .docx].include?(file_extension)
  end

  def is_image?
    %w[.jpg .jpeg .png .gif .webp].include?(file_extension)
  end

  def file_type
    if is_pdf?
      'pdf'
    elsif is_text?
      'text'
    elsif is_image?
      'image'
    else
      'other'
    end
  end

  def download_url
    return file_url if file_url.present?

    nil
  rescue StandardError => e
    Rails.logger.error "Error generating download URL: #{e.message}"
    nil
  end
end
