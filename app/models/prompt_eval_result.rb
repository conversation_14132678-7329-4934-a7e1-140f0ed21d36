# frozen_string_literal: true

class PromptEvalResult < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :prompt_eval
  belongs_to :model_bank

  enum status: string_enum('pending', 'running', 'completed', 'failed')

  def mark_as_completed!
    update!(status: 'completed')
  end

  def mark_as_failed!(error_message = nil)
    update!(status: 'failed', error_message: error_message)
  end
end
